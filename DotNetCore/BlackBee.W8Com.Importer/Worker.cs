using BlackBee.W8Com.EF.Contexts;
using BlackBee.W8Com.EF.Initializer;
using Microsoft.EntityFrameworkCore;

namespace BlackBee.W8Com.Importer
{
    public class Worker : BackgroundService
    {
        private readonly ILogger<Worker> _logger;
        private readonly IServiceScopeFactory _serviceScopeFactory;
        private readonly IConfiguration _configuration;
        
        public Worker(ILogger<Worker> logger, IServiceScopeFactory serviceScopeFactory, IConfiguration configuration)
        {
            _logger = logger;
            _serviceScopeFactory = serviceScopeFactory;
            _configuration = configuration;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            using (var scope = _serviceScopeFactory.CreateScope())
            {
                W8ComDbContext context = scope.ServiceProvider.GetRequiredService<W8ComDbContext>();

                context.Database.Migrate();

                W8ComDataInitializer.SeedData(scope, _configuration, context);
            }

            while (!stoppingToken.IsCancellationRequested)
            {
                _logger.LogInformation("Worker running at: {time}", DateTimeOffset.Now);
                await Task.Delay(1000, stoppingToken);
            }
        }
    }
}