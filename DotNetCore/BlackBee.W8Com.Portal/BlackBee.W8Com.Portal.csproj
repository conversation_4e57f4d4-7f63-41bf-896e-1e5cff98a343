<Project Sdk="Microsoft.NET.Sdk.Web">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
  </PropertyGroup>
  <ItemGroup>
    <Folder Include="Log\" />
    <Folder Include="StaticFiles\" />
    <Folder Include="wwwroot\assets\libs\choices.js\src\" />
    <Folder Include="wwwroot\assets\libs\flatpickr\types\" />
    <Folder Include="wwwroot\assets\libs\flatpickr\utils\" />
    <Folder Include="wwwroot\assets\libs\gmaps\docs\" />
    <Folder Include="wwwroot\assets\libs\gmaps\examples\" />
    <Folder Include="wwwroot\assets\libs\gmaps\test\" />
    <Folder Include="wwwroot\assets\libs\gridjs\src\" />
    <Folder Include="wwwroot\assets\libs\moment\src\" />
    <Folder Include="wwwroot\assets\libs\particles.js\demo\" />
    <Folder Include="wwwroot\assets\libs\rater-js\example\" />
    <Folder Include="wwwroot\assets\libs\rater-js\test\" />
    <Folder Include="wwwroot\assets\libs\swiper\angular\angular\src\" />
    <Folder Include="wwwroot\assets\libs\swiper\angular\esm2015\angular\src\" />
    <Folder Include="wwwroot\assets\libs\swiper\types\modules\" />
    <Folder Include="wwwroot\assets\libs\toastify-js\example\" />
    <Folder Include="wwwroot\assets\libs\toastify-js\src\" />
  </ItemGroup>
  <ItemGroup>
    <None Include="wwwroot\assets\images\logo\BlackBee Icon 32x32.png" />
    <None Include="wwwroot\assets\images\logo\BlackBee White Logo.png" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="BlackBee.W8Auth.Api.Model" Version="8.1.7" />
    <PackageReference Include="BlackBee.W8Auth.Api.Wrapper" Version="8.1.7" />
    <PackageReference Include="BlackBee.W8Base.EF" Version="8.1.7" />
    <PackageReference Include="BlackBee.W8Base.Web" Version="8.1.7" />
    <PackageReference Include="OrchardCore.Localization.Core" Version="2.1.7" />
    <PackageReference Include="Serilog.AspNetCore" Version="8.0.3" />
    <PackageReference Include="Serilog.Enrichers.Environment" Version="3.0.1" />
    <PackageReference Include="Serilog.Enrichers.Thread" Version="4.0.0" />
    <PackageReference Include="Serilog.Sinks.Async" Version="2.1.0" />
    <PackageReference Include="Serilog.Sinks.File" Version="7.0.0" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.12.1" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\BlackBee.W8Com.Api.Wrapper\BlackBee.W8Com.Api.Wrapper.csproj" />
    <ProjectReference Include="..\BlackBee.W8Com.EF\BlackBee.W8Com.EF.csproj" />
  </ItemGroup>
  <ItemGroup>
    <None Update="fr.po">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="Localization\cs.po">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="Localization\fr.po">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>
</Project>