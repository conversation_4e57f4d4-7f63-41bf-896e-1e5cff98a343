using BlackBee.W8Base.Api.Wrapper.Handler;
using BlackBee.W8Base.Caching;
using BlackBee.W8Base.Common;
using BlackBee.W8Base.FileStorage;
using BlackBee.W8Base.Handler;
using BlackBee.W8Base.Mailing;
using BlackBee.W8Base.Web.Auth.Jwt;
using BlackBee.W8Base.Web.TicketStores;
using BlackBee.W8Com.Portal.Models;
using FluentValidation;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Diagnostics.HealthChecks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Routing;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Options;
using System;
using System.IO;
using System.Reflection;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace BlackBee.W8Com.Portal;

public static class Startup
{
    public static IServiceCollection AddApplication(this IServiceCollection services)
    {
        var assembly = Assembly.GetExecutingAssembly();

        return services.AddValidatorsFromAssembly(assembly);
    }

    public static IServiceCollection AddInfrastructure(this IServiceCollection services, IConfiguration config)
    {
        services.AddSettings(config)
            .AddJwtAuth(config)
            .AddAuthentication(config)
            .AddCaching(config)
            .AddHealthCheck(config)
            .AddMailing(config)
            .AddRouting(options => options.LowercaseUrls = true)
            .AddServices()
            .AddControllersWithViews();

        services.Configure<DataProtectionTokenProviderOptions>(o => o.TokenLifespan = TimeSpan.FromHours(3));

        //services.AddResponseCaching();

        services.AddPortableObjectLocalization(options => options.ResourcesPath = "Localization");

        services
            .Configure<RequestLocalizationOptions>(options => options
                .AddSupportedCultures("fr", "cs")
                .AddSupportedUICultures("fr", "cs"));

        services
            .AddRazorPages()
            .AddViewLocalization();

        return services;
    }

    public static IApplicationBuilder UseInfrastructure(this IApplicationBuilder builder, IConfiguration config) =>
        builder
            .UseRequestLocalization()
            .UseStaticFiles()
            .UseFileStorage()
            .UseRouting()
            .UseAuthentication()
            .UseAuthorization()
            .UseMiddleware<CustomCookieAuthenticationMiddleware<BearableService>>()
            .UseCookiePolicy();


    public static IEndpointRouteBuilder MapEndpoints(this IEndpointRouteBuilder builder)
    {
        builder.MapHealthCheck();
        return builder;
    }

    private static IServiceCollection AddSettings(this IServiceCollection services, IConfiguration config)
    {
        services.Configure<SiteSettings>(config.GetSection(SiteSettings.SectionName));
        services.AddSingleton(s => s.GetRequiredService<IOptions<SiteSettings>>().Value);
        return services;
    }

    #region Custom

    /// <summary>
    /// Add services for authentication, including Identity model, IdentityServer4 and external providers
    /// </summary>
    /// <param name="services"></param>
    /// <param name="config"></param>
    private static IServiceCollection AddAuthentication(this IServiceCollection services, IConfiguration config)
    {
        services.Configure<IISOptions>(iis =>
        {
            iis.AuthenticationDisplayName = "Windows";
            iis.AutomaticAuthentication = false;
        });

        services.Configure<CookiePolicyOptions>(options =>
        {
            // This lambda determines whether user consent for non-essential cookies is needed for a given request.
            options.CheckConsentNeeded = context => true;
            options.MinimumSameSitePolicy = SameSiteMode.Lax;
            options.ConsentCookieValue = "true";
        });

        //Used by Context to get User
        services.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();

        services.Configure<IdentityOptions>(options =>
        {
            // Password settings.
            options.Password.RequireDigit = true;
            options.Password.RequireLowercase = true;
            options.Password.RequireNonAlphanumeric = true;
            options.Password.RequireUppercase = true;
            options.Password.RequiredLength = 6;
            options.Password.RequiredUniqueChars = 1;

            // Lockout settings.
            options.Lockout.DefaultLockoutTimeSpan = TimeSpan.FromMinutes(5);
            options.Lockout.MaxFailedAccessAttempts = 5;
            options.Lockout.AllowedForNewUsers = true;

            // User settings.
            options.User.AllowedUserNameCharacters = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789-._@+";
            options.User.RequireUniqueEmail = true;
        });

        var cacheId = config["SiteSettings:Platform"] ?? "W8Com";

        services.AddAuthentication(CookieAuthenticationDefaults.AuthenticationScheme)
            .AddCookie(options =>
            {
                options.Cookie.HttpOnly = true;

                //This overrrides all even if the jwt has longer expiration
                options.ExpireTimeSpan = TimeSpan.FromDays(7);
                options.Cookie.MaxAge = options.ExpireTimeSpan;
                options.SlidingExpiration = true;
                options.EventsType = typeof(CustomCookieAuthenticationEvents<BearableService>);

                options.LoginPath = "/Authentication/SignInBasic";
                options.LogoutPath = "/Authentication/SignOutBasic";
                options.AccessDeniedPath = "/Errors/Errors401";

                var cache = services.BuildServiceProvider().GetService<ICacheService>();
                options.SessionStore = new CacheServiceTicketStore(cache, cacheId);
            });

        services.AddTransient<W8AuthenticationHelper<BearableService>>();
        services.AddTransient<CustomCookieAuthenticationEvents<BearableService>>();

        return services;
    }

    #endregion

    #region HealthCheck

    private static IEndpointConventionBuilder MapHealthCheck(this IEndpointRouteBuilder endpoints)
    {
        var item = endpoints.MapHealthChecks("/portal/health", new HealthCheckOptions
        {
            AllowCachingResponses = false,
            ResultStatusCodes =
                {
                    [HealthStatus.Healthy] = StatusCodes.Status200OK,
                    [HealthStatus.Degraded] = StatusCodes.Status200OK,
                    [HealthStatus.Unhealthy] = StatusCodes.Status503ServiceUnavailable
                },
            ResponseWriter = WriteResponse
        }).AllowAnonymous();

        return item;
    }

    private static Task WriteResponse(HttpContext context, HealthReport healthReport)
    {
        context.Response.ContentType = "application/json; charset=utf-8";

        var options = new JsonWriterOptions { Indented = true };

        using var memoryStream = new MemoryStream();
        using (var jsonWriter = new Utf8JsonWriter(memoryStream, options))
        {
            jsonWriter.WriteStartObject();
            jsonWriter.WriteString("status", healthReport.Status.ToString());
            jsonWriter.WriteStartObject("results");

            foreach (var healthReportEntry in healthReport.Entries)
            {
                jsonWriter.WriteStartObject(healthReportEntry.Key);
                jsonWriter.WriteString("status", healthReportEntry.Value.Status.ToString());
                jsonWriter.WriteString("description", healthReportEntry.Value.Description);
                jsonWriter.WriteStartObject("data");

                foreach (var item in healthReportEntry.Value.Data)
                {
                    jsonWriter.WritePropertyName(item.Key);

                    JsonSerializer.Serialize(jsonWriter, item.Value,
                        item.Value?.GetType() ?? typeof(object));
                }

                jsonWriter.WriteEndObject();
                jsonWriter.WriteEndObject();
            }

            jsonWriter.WriteEndObject();
            jsonWriter.WriteEndObject();
        }

        return context.Response.WriteAsync(Encoding.UTF8.GetString(memoryStream.ToArray()));
    }

    private static IServiceCollection AddHealthCheck(this IServiceCollection services, IConfiguration config)
    {
        var items = services.AddHealthChecks()
            .AddCheck<PortalHealthCheck>("portal").Services;

        return items;
    }

    public class PortalHealthCheck : IHealthCheck
    {
        public Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
        {
            var isHealthy = true;

            if (isHealthy)
            {
                return Task.FromResult(
                    HealthCheckResult.Healthy("A healthy result."));
            }

            return Task.FromResult(
                new HealthCheckResult(
                    context.Registration.FailureStatus, "An unhealthy result."));
        }
    }

    #endregion
}