{"name": "sortablejs", "exportName": "Sortable", "version": "1.15.0", "devDependencies": {"@babel/core": "^7.4.4", "@babel/plugin-transform-object-assign": "^7.2.0", "@babel/preset-env": "^7.4.4", "rollup": "^1.11.3", "rollup-plugin-babel": "^4.3.2", "rollup-plugin-json": "^4.0.0", "rollup-plugin-node-resolve": "^5.0.0", "testcafe": "^1.3.1", "testcafe-browser-provider-saucelabs": "^1.7.0", "testcafe-reporter-xunit": "^2.1.0", "uglify-js": "^3.5.12"}, "description": "JavaScript library for reorderable drag-and-drop lists on modern browsers and touch devices. No jQuery required. Supports Meteor, AngularJS, React, Polymer, Vue, Knockout and any CSS library, e.g. Bootstrap.", "main": "./Sortable.min.js", "module": "modular/sortable.esm.js", "scripts": {"build:umd": "set NODE_ENV=umd&& rollup -c ./scripts/umd-build.js", "build:umd:watch": "set NODE_ENV=umd&& rollup -w -c ./scripts/umd-build.js", "build:es": "set NODE_ENV=es&& rollup -c ./scripts/esm-build.js", "build:es:watch": "set NODE_ENV=es&& rollup -w -c ./scripts/esm-build.js", "minify": "node ./scripts/minify.js", "build": "npm run build:es && npm run build:umd && npm run minify", "test:compat": "node ./scripts/test-compat.js", "test": "node ./scripts/test.js"}, "maintainers": ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>"], "repository": {"type": "git", "url": "git://github.com/SortableJS/Sortable.git"}, "files": ["Sortable.js", "Sortable.min.js", "modular/"], "keywords": ["sortable", "reorder", "drag", "meteor", "angular", "ng-sortable", "react", "vue", "mixin"], "license": "MIT"}