{"version": 3, "file": "bundle.es.js", "sources": ["../src/index.js"], "sourcesContent": ["import {boolean} from 'type-func';\r\nimport {\r\n    requestAnimation<PERSON>rame as requestFrame,\r\n    cancelAnimation<PERSON>rame as cancelFrame\r\n} from 'animation-frame-polyfill';\r\nimport {\r\n    hasElement,\r\n    addElements,\r\n    removeElements\r\n} from 'dom-set';\r\n\r\nimport {\r\n    createPointCB,\r\n    getClientRect as getRect,\r\n    pointInside\r\n} from 'dom-plane';\r\n\r\nimport mousemoveDispatcher from 'dom-mousemove-dispatcher';\r\n\r\nfunction AutoScroller(elements, options = {}){\r\n    const self = this;\r\n    let maxSpeed = 4, scrolling = false;\r\n\r\n    this.margin = options.margin || -1;\r\n    //this.scrolling = false;\r\n    this.scrollWhenOutside = options.scrollWhenOutside || false;\r\n\r\n    let point = {},\r\n        pointCB = createPointCB(point),\r\n        dispatcher = mousemoveDispatcher(),\r\n        down = false;\r\n\r\n    window.addEventListener('mousemove', pointCB, false);\r\n    window.addEventListener('touchmove', pointCB, false);\r\n\r\n    if(!isNaN(options.maxSpeed)){\r\n        maxSpeed = options.maxSpeed;\r\n    }\r\n\r\n    this.autoScroll = boolean(options.autoScroll);\r\n    this.syncMove = boolean(options.syncMove, false);\r\n\r\n    this.destroy = function(forceCleanAnimation) {\r\n        window.removeEventListener('mousemove', pointCB, false);\r\n        window.removeEventListener('touchmove', pointCB, false);\r\n        window.removeEventListener('mousedown', onDown, false);\r\n        window.removeEventListener('touchstart', onDown, false);\r\n        window.removeEventListener('mouseup', onUp, false);\r\n        window.removeEventListener('touchend', onUp, false);\r\n        window.removeEventListener('pointerup', onUp, false);\r\n        window.removeEventListener('mouseleave', onMouseOut, false);\r\n\r\n        window.removeEventListener('mousemove', onMove, false);\r\n        window.removeEventListener('touchmove', onMove, false);\r\n\r\n        window.removeEventListener('scroll', setScroll, true);\r\n        elements = [];\r\n        if(forceCleanAnimation){\r\n          cleanAnimation();\r\n        }\r\n    };\r\n\r\n    this.add = function(...element){\r\n        addElements(elements, ...element);\r\n        return this;\r\n    };\r\n\r\n    this.remove = function(...element){\r\n        return removeElements(elements, ...element);\r\n    };\r\n\r\n    let hasWindow = null, windowAnimationFrame;\r\n\r\n    if(Object.prototype.toString.call(elements) !== '[object Array]'){\r\n        elements = [elements];\r\n    }\r\n\r\n    (function(temp){\r\n        elements = [];\r\n        temp.forEach(function(element){\r\n            if(element === window){\r\n                hasWindow = window;\r\n            }else{\r\n                self.add(element);\r\n            }\r\n        })\r\n    }(elements));\r\n\r\n    Object.defineProperties(this, {\r\n        down: {\r\n            get: function(){ return down; }\r\n        },\r\n        maxSpeed: {\r\n            get: function(){ return maxSpeed; }\r\n        },\r\n        point: {\r\n            get: function(){ return point; }\r\n        },\r\n        scrolling: {\r\n            get: function(){ return scrolling; }\r\n        }\r\n    });\r\n\r\n    let n = 0, current = null, animationFrame;\r\n\r\n    window.addEventListener('mousedown', onDown, false);\r\n    window.addEventListener('touchstart', onDown, false);\r\n    window.addEventListener('mouseup', onUp, false);\r\n    window.addEventListener('touchend', onUp, false);\r\n\r\n    /*\r\n    IE does not trigger mouseup event when scrolling.\r\n    It is a known issue that Microsoft won't fix.\r\n    https://connect.microsoft.com/IE/feedback/details/783058/scrollbar-trigger-mousedown-but-not-mouseup\r\n    IE supports pointer events instead\r\n    */\r\n    window.addEventListener('pointerup', onUp, false);\r\n\r\n    window.addEventListener('mousemove', onMove, false);\r\n    window.addEventListener('touchmove', onMove, false);\r\n\r\n    window.addEventListener('mouseleave', onMouseOut, false);\r\n\r\n    window.addEventListener('scroll', setScroll, true);\r\n\r\n    function setScroll(e){\r\n\r\n        for(let i=0; i<elements.length; i++){\r\n            if(elements[i] === e.target){\r\n                scrolling = true;\r\n                break;\r\n            }\r\n        }\r\n\r\n        if(scrolling){\r\n            requestFrame(()=>scrolling = false)\r\n        }\r\n    }\r\n\r\n    function onDown(){\r\n        down = true;\r\n    }\r\n\r\n    function onUp(){\r\n        down = false;\r\n        cleanAnimation();\r\n    }\r\n    function cleanAnimation(){\r\n      cancelFrame(animationFrame);\r\n      cancelFrame(windowAnimationFrame);\r\n    }\r\n    function onMouseOut(){\r\n        down = false;\r\n    }\r\n\r\n    function getTarget(target){\r\n        if(!target){\r\n            return null;\r\n        }\r\n\r\n        if(current === target){\r\n            return target;\r\n        }\r\n\r\n        if(hasElement(elements, target)){\r\n            return target;\r\n        }\r\n\r\n        while(target = target.parentNode){\r\n            if(hasElement(elements, target)){\r\n                return target;\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    function getElementUnderPoint(){\r\n        let underPoint = null;\r\n\r\n        for(var i=0; i<elements.length; i++){\r\n            if(inside(point, elements[i])){\r\n                underPoint = elements[i];\r\n            }\r\n        }\r\n\r\n        return underPoint;\r\n    }\r\n\r\n\r\n    function onMove(event){\r\n\r\n        if(!self.autoScroll()) return;\r\n\r\n        if(event['dispatched']){ return; }\r\n\r\n        let target = event.target, body = document.body;\r\n\r\n        if(current && !inside(point, current)){\r\n            if(!self.scrollWhenOutside){\r\n                current = null;\r\n            }\r\n        }\r\n\r\n        if(target && target.parentNode === body){\r\n            //The special condition to improve speed.\r\n            target = getElementUnderPoint();\r\n        }else{\r\n            target = getTarget(target);\r\n\r\n            if(!target){\r\n                target = getElementUnderPoint();\r\n            }\r\n        }\r\n\r\n\r\n        if(target && target !== current){\r\n            current = target;\r\n        }\r\n\r\n        if(hasWindow){\r\n            cancelFrame(windowAnimationFrame);\r\n            windowAnimationFrame = requestFrame(scrollWindow);\r\n        }\r\n\r\n\r\n        if(!current){\r\n            return;\r\n        }\r\n\r\n        cancelFrame(animationFrame);\r\n        animationFrame = requestFrame(scrollTick);\r\n    }\r\n\r\n    function scrollWindow(){\r\n        autoScroll(hasWindow);\r\n\r\n        cancelFrame(windowAnimationFrame);\r\n        windowAnimationFrame = requestFrame(scrollWindow);\r\n    }\r\n\r\n    function scrollTick(){\r\n\r\n        if(!current){\r\n            return;\r\n        }\r\n\r\n        autoScroll(current);\r\n\r\n        cancelFrame(animationFrame);\r\n        animationFrame = requestFrame(scrollTick);\r\n\r\n    }\r\n\r\n\r\n    function autoScroll(el){\r\n        let rect = getRect(el), scrollx, scrolly;\r\n\r\n        if(point.x < rect.left + self.margin){\r\n            scrollx = Math.floor(\r\n                Math.max(-1, (point.x - rect.left) / self.margin - 1) * self.maxSpeed\r\n            );\r\n        }else if(point.x > rect.right - self.margin){\r\n            scrollx = Math.ceil(\r\n                Math.min(1, (point.x - rect.right) / self.margin + 1) * self.maxSpeed\r\n            );\r\n        }else{\r\n            scrollx = 0;\r\n        }\r\n\r\n        if(point.y < rect.top + self.margin){\r\n            scrolly = Math.floor(\r\n                Math.max(-1, (point.y - rect.top) / self.margin - 1) * self.maxSpeed\r\n            );\r\n        }else if(point.y > rect.bottom - self.margin){\r\n            scrolly = Math.ceil(\r\n                Math.min(1, (point.y - rect.bottom) / self.margin + 1) * self.maxSpeed\r\n            );\r\n        }else{\r\n            scrolly = 0;\r\n        }\r\n\r\n        if(self.syncMove()){\r\n            /*\r\n            Notes about mousemove event dispatch.\r\n            screen(X/Y) should need to be updated.\r\n            Some other properties might need to be set.\r\n            Keep the syncMove option default false until all inconsistencies are taken care of.\r\n            */\r\n            dispatcher.dispatch(el, {\r\n                pageX: point.pageX + scrollx,\r\n                pageY: point.pageY + scrolly,\r\n                clientX: point.x + scrollx,\r\n                clientY: point.y + scrolly\r\n            });\r\n        }\r\n\r\n        setTimeout(()=>{\r\n\r\n            if(scrolly){\r\n                scrollY(el, scrolly);\r\n            }\r\n\r\n            if(scrollx){\r\n                scrollX(el, scrollx);\r\n            }\r\n\r\n        });\r\n    }\r\n\r\n    function scrollY(el, amount){\r\n        if(el === window){\r\n            window.scrollTo(el.pageXOffset, el.pageYOffset + amount);\r\n        }else{\r\n            el.scrollTop += amount;\r\n        }\r\n    }\r\n\r\n    function scrollX(el, amount){\r\n        if(el === window){\r\n            window.scrollTo(el.pageXOffset + amount, el.pageYOffset);\r\n        }else{\r\n            el.scrollLeft += amount;\r\n        }\r\n    }\r\n\r\n}\r\n\r\nexport default function AutoScrollerFactory(element, options){\r\n    return new AutoScroller(element, options);\r\n}\r\n\r\nfunction inside(point, el, rect){\r\n    if(!rect){\r\n        return pointInside(point, el);\r\n    }else{\r\n        return (point.y > rect.top && point.y < rect.bottom &&\r\n                point.x > rect.left && point.x < rect.right);\r\n    }\r\n}\r\n\r\n/*\r\ngit remote add origin https://github.com/hollowdoor/dom_autoscroller.git\r\ngit push -u origin master\r\n*/\r\n"], "names": ["const", "let", "requestFrame", "cancelFrame", "getRect"], "mappings": ";;;;;;AAmBA,SAAS,YAAY,CAAC,QAAQ,EAAE,OAAY,CAAC;qCAAN,GAAG,EAAE;;IACxCA,IAAM,IAAI,GAAG,IAAI,CAAC;IAClBC,IAAI,QAAQ,GAAG,CAAC,EAAE,SAAS,GAAG,KAAK,CAAC;;IAEpC,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,IAAI,CAAC,CAAC,CAAC;;IAEnC,IAAI,CAAC,iBAAiB,GAAG,OAAO,CAAC,iBAAiB,IAAI,KAAK,CAAC;;IAE5DA,IAAI,KAAK,GAAG,EAAE;QACV,OAAO,GAAG,aAAa,CAAC,KAAK,CAAC;QAC9B,UAAU,GAAG,mBAAmB,EAAE;QAClC,IAAI,GAAG,KAAK,CAAC;;IAEjB,MAAM,CAAC,gBAAgB,CAAC,WAAW,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;IACrD,MAAM,CAAC,gBAAgB,CAAC,WAAW,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;;IAErD,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QACxB,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;KAC/B;;IAED,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;IAC9C,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,OAAO,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;;IAEjD,IAAI,CAAC,OAAO,GAAG,SAAS,mBAAmB,EAAE;QACzC,MAAM,CAAC,mBAAmB,CAAC,WAAW,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;QACxD,MAAM,CAAC,mBAAmB,CAAC,WAAW,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;QACxD,MAAM,CAAC,mBAAmB,CAAC,WAAW,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;QACvD,MAAM,CAAC,mBAAmB,CAAC,YAAY,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;QACxD,MAAM,CAAC,mBAAmB,CAAC,SAAS,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;QACnD,MAAM,CAAC,mBAAmB,CAAC,UAAU,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;QACpD,MAAM,CAAC,mBAAmB,CAAC,WAAW,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;QACrD,MAAM,CAAC,mBAAmB,CAAC,YAAY,EAAE,UAAU,EAAE,KAAK,CAAC,CAAC;;QAE5D,MAAM,CAAC,mBAAmB,CAAC,WAAW,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;QACvD,MAAM,CAAC,mBAAmB,CAAC,WAAW,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;;QAEvD,MAAM,CAAC,mBAAmB,CAAC,QAAQ,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;QACtD,QAAQ,GAAG,EAAE,CAAC;QACd,GAAG,mBAAmB,CAAC;UACrB,cAAc,EAAE,CAAC;SAClB;KACJ,CAAC;;IAEF,IAAI,CAAC,GAAG,GAAG,UAAoB;;;;QAC3B,WAAW,MAAA,CAAC,UAAA,QAAQ,WAAE,OAAU,EAAA,CAAC,CAAC;QAClC,OAAO,IAAI,CAAC;KACf,CAAC;;IAEF,IAAI,CAAC,MAAM,GAAG,UAAoB;;;;QAC9B,OAAO,cAAc,MAAA,CAAC,UAAA,QAAQ,WAAE,OAAU,EAAA,CAAC,CAAC;KAC/C,CAAC;;IAEFA,IAAI,SAAS,GAAG,IAAI,EAAE,oBAAoB,CAAC;;IAE3C,GAAG,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,gBAAgB,CAAC;QAC7D,QAAQ,GAAG,CAAC,QAAQ,CAAC,CAAC;KACzB;;IAED,CAAC,SAAS,IAAI,CAAC;QACX,QAAQ,GAAG,EAAE,CAAC;QACd,IAAI,CAAC,OAAO,CAAC,SAAS,OAAO,CAAC;YAC1B,GAAG,OAAO,KAAK,MAAM,CAAC;gBAClB,SAAS,GAAG,MAAM,CAAC;aACtB,IAAI;gBACD,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;aACrB;SACJ,CAAC,CAAA;KACL,CAAC,QAAQ,CAAC,EAAE;;IAEb,MAAM,CAAC,gBAAgB,CAAC,IAAI,EAAE;QAC1B,IAAI,EAAE;YACF,GAAG,EAAE,UAAU,EAAE,OAAO,IAAI,CAAC,EAAE;SAClC;QACD,QAAQ,EAAE;YACN,GAAG,EAAE,UAAU,EAAE,OAAO,QAAQ,CAAC,EAAE;SACtC;QACD,KAAK,EAAE;YACH,GAAG,EAAE,UAAU,EAAE,OAAO,KAAK,CAAC,EAAE;SACnC;QACD,SAAS,EAAE;YACP,GAAG,EAAE,UAAU,EAAE,OAAO,SAAS,CAAC,EAAE;SACvC;KACJ,CAAC,CAAC;;IAEHA,IAAI,CAAC,GAAG,CAAC,EAAE,OAAO,GAAG,IAAI,EAAE,cAAc,CAAC;;IAE1C,MAAM,CAAC,gBAAgB,CAAC,WAAW,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;IACpD,MAAM,CAAC,gBAAgB,CAAC,YAAY,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;IACrD,MAAM,CAAC,gBAAgB,CAAC,SAAS,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;IAChD,MAAM,CAAC,gBAAgB,CAAC,UAAU,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;;;;;;;;IAQjD,MAAM,CAAC,gBAAgB,CAAC,WAAW,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;;IAElD,MAAM,CAAC,gBAAgB,CAAC,WAAW,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;IACpD,MAAM,CAAC,gBAAgB,CAAC,WAAW,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;;IAEpD,MAAM,CAAC,gBAAgB,CAAC,YAAY,EAAE,UAAU,EAAE,KAAK,CAAC,CAAC;;IAEzD,MAAM,CAAC,gBAAgB,CAAC,QAAQ,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;;IAEnD,SAAS,SAAS,CAAC,CAAC,CAAC;;QAEjB,IAAIA,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC;YAChC,GAAG,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;gBACxB,SAAS,GAAG,IAAI,CAAC;gBACjB,MAAM;aACT;SACJ;;QAED,GAAG,SAAS,CAAC;YACTC,qBAAY,CAAC,WAAE,SAAE,SAAS,GAAG,KAAK,GAAA,CAAC,CAAA;SACtC;KACJ;;IAED,SAAS,MAAM,EAAE;QACb,IAAI,GAAG,IAAI,CAAC;KACf;;IAED,SAAS,IAAI,EAAE;QACX,IAAI,GAAG,KAAK,CAAC;QACb,cAAc,EAAE,CAAC;KACpB;IACD,SAAS,cAAc,EAAE;MACvBC,oBAAW,CAAC,cAAc,CAAC,CAAC;MAC5BA,oBAAW,CAAC,oBAAoB,CAAC,CAAC;KACnC;IACD,SAAS,UAAU,EAAE;QACjB,IAAI,GAAG,KAAK,CAAC;KAChB;;IAED,SAAS,SAAS,CAAC,MAAM,CAAC;QACtB,GAAG,CAAC,MAAM,CAAC;YACP,OAAO,IAAI,CAAC;SACf;;QAED,GAAG,OAAO,KAAK,MAAM,CAAC;YAClB,OAAO,MAAM,CAAC;SACjB;;QAED,GAAG,UAAU,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;YAC5B,OAAO,MAAM,CAAC;SACjB;;QAED,MAAM,MAAM,GAAG,MAAM,CAAC,UAAU,CAAC;YAC7B,GAAG,UAAU,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;gBAC5B,OAAO,MAAM,CAAC;aACjB;SACJ;;QAED,OAAO,IAAI,CAAC;KACf;;IAED,SAAS,oBAAoB,EAAE;QAC3BF,IAAI,UAAU,GAAG,IAAI,CAAC;;QAEtB,IAAI,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC;YAChC,GAAG,MAAM,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC1B,UAAU,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;aAC5B;SACJ;;QAED,OAAO,UAAU,CAAC;KACrB;;;IAGD,SAAS,MAAM,CAAC,KAAK,CAAC;;QAElB,GAAG,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,EAAA,OAAO,EAAA;;QAE9B,GAAG,KAAK,CAAC,YAAY,CAAC,CAAC,EAAE,OAAO,EAAE;;QAElCA,IAAI,MAAM,GAAG,KAAK,CAAC,MAAM,EAAE,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC;;QAEhD,GAAG,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;YAClC,GAAG,CAAC,IAAI,CAAC,iBAAiB,CAAC;gBACvB,OAAO,GAAG,IAAI,CAAC;aAClB;SACJ;;QAED,GAAG,MAAM,IAAI,MAAM,CAAC,UAAU,KAAK,IAAI,CAAC;;YAEpC,MAAM,GAAG,oBAAoB,EAAE,CAAC;SACnC,IAAI;YACD,MAAM,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC;;YAE3B,GAAG,CAAC,MAAM,CAAC;gBACP,MAAM,GAAG,oBAAoB,EAAE,CAAC;aACnC;SACJ;;;QAGD,GAAG,MAAM,IAAI,MAAM,KAAK,OAAO,CAAC;YAC5B,OAAO,GAAG,MAAM,CAAC;SACpB;;QAED,GAAG,SAAS,CAAC;YACTE,oBAAW,CAAC,oBAAoB,CAAC,CAAC;YAClC,oBAAoB,GAAGD,qBAAY,CAAC,YAAY,CAAC,CAAC;SACrD;;;QAGD,GAAG,CAAC,OAAO,CAAC;YACR,OAAO;SACV;;QAEDC,oBAAW,CAAC,cAAc,CAAC,CAAC;QAC5B,cAAc,GAAGD,qBAAY,CAAC,UAAU,CAAC,CAAC;KAC7C;;IAED,SAAS,YAAY,EAAE;QACnB,UAAU,CAAC,SAAS,CAAC,CAAC;;QAEtBC,oBAAW,CAAC,oBAAoB,CAAC,CAAC;QAClC,oBAAoB,GAAGD,qBAAY,CAAC,YAAY,CAAC,CAAC;KACrD;;IAED,SAAS,UAAU,EAAE;;QAEjB,GAAG,CAAC,OAAO,CAAC;YACR,OAAO;SACV;;QAED,UAAU,CAAC,OAAO,CAAC,CAAC;;QAEpBC,oBAAW,CAAC,cAAc,CAAC,CAAC;QAC5B,cAAc,GAAGD,qBAAY,CAAC,UAAU,CAAC,CAAC;;KAE7C;;;IAGD,SAAS,UAAU,CAAC,EAAE,CAAC;QACnBD,IAAI,IAAI,GAAGG,aAAO,CAAC,EAAE,CAAC,EAAE,OAAO,EAAE,OAAO,CAAC;;QAEzC,GAAG,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC;YACjC,OAAO,GAAG,IAAI,CAAC,KAAK;gBAChB,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ;aACxE,CAAC;SACL,KAAK,GAAG,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;YACxC,OAAO,GAAG,IAAI,CAAC,IAAI;gBACf,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ;aACxE,CAAC;SACL,IAAI;YACD,OAAO,GAAG,CAAC,CAAC;SACf;;QAED,GAAG,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC;YAChC,OAAO,GAAG,IAAI,CAAC,KAAK;gBAChB,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ;aACvE,CAAC;SACL,KAAK,GAAG,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;YACzC,OAAO,GAAG,IAAI,CAAC,IAAI;gBACf,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ;aACzE,CAAC;SACL,IAAI;YACD,OAAO,GAAG,CAAC,CAAC;SACf;;QAED,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;;;;;;;YAOf,UAAU,CAAC,QAAQ,CAAC,EAAE,EAAE;gBACpB,KAAK,EAAE,KAAK,CAAC,KAAK,GAAG,OAAO;gBAC5B,KAAK,EAAE,KAAK,CAAC,KAAK,GAAG,OAAO;gBAC5B,OAAO,EAAE,KAAK,CAAC,CAAC,GAAG,OAAO;gBAC1B,OAAO,EAAE,KAAK,CAAC,CAAC,GAAG,OAAO;aAC7B,CAAC,CAAC;SACN;;QAED,UAAU,CAAC,WAAE;;YAET,GAAG,OAAO,CAAC;gBACP,OAAO,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;aACxB;;YAED,GAAG,OAAO,CAAC;gBACP,OAAO,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;aACxB;;SAEJ,CAAC,CAAC;KACN;;IAED,SAAS,OAAO,CAAC,EAAE,EAAE,MAAM,CAAC;QACxB,GAAG,EAAE,KAAK,MAAM,CAAC;YACb,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,WAAW,EAAE,EAAE,CAAC,WAAW,GAAG,MAAM,CAAC,CAAC;SAC5D,IAAI;YACD,EAAE,CAAC,SAAS,IAAI,MAAM,CAAC;SAC1B;KACJ;;IAED,SAAS,OAAO,CAAC,EAAE,EAAE,MAAM,CAAC;QACxB,GAAG,EAAE,KAAK,MAAM,CAAC;YACb,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,WAAW,GAAG,MAAM,EAAE,EAAE,CAAC,WAAW,CAAC,CAAC;SAC5D,IAAI;YACD,EAAE,CAAC,UAAU,IAAI,MAAM,CAAC;SAC3B;KACJ;;CAEJ;;AAED,AAAe,SAAS,mBAAmB,CAAC,OAAO,EAAE,OAAO,CAAC;IACzD,OAAO,IAAI,YAAY,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;CAC7C;;AAED,SAAS,MAAM,CAAC,KAAK,EAAE,EAAE,EAAE,IAAI,CAAC;IAC5B,GAAG,CAAC,IAAI,CAAC;QACL,OAAO,WAAW,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;KACjC,IAAI;QACD,QAAQ,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM;gBAC3C,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,IAAI,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,EAAE;KACxD;CACJ;;;;;;;"}