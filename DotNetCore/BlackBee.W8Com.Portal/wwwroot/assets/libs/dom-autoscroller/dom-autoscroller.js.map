{"version": 3, "file": "dom-autoscroller.js", "sources": ["../node_modules/type-func/dist/bundle.es.js", "../node_modules/animation-frame-polyfill/lib/animation-frame-polyfill.module.js", "../node_modules/array-from/polyfill.js", "../node_modules/array-from/index.js", "../node_modules/is-array/index.js", "../node_modules/iselement/module/index.js", "../node_modules/dom-set/dist/bundle.es.js", "../node_modules/create-point-cb/dist/bundle.es.js", "../node_modules/dom-plane/dist/bundle.es.js", "../node_modules/dom-mousemove-dispatcher/dist/bundle.es.js", "../src/index.js"], "sourcesContent": ["function getDef(f, d) {\n    if (typeof f === 'undefined') {\n        return typeof d === 'undefined' ? f : d;\n    }\n\n    return f;\n}\nfunction boolean(func, def) {\n\n    func = getDef(func, def);\n\n    if (typeof func === 'function') {\n        return function f() {\n            for (var _len = arguments.length, args = Array(_len), _key = 0; _key < _len; _key++) {\n                args[_key] = arguments[_key];\n            }\n\n            return !!func.apply(this, args);\n        };\n    }\n\n    return !!func ? function () {\n        return true;\n    } : function () {\n        return false;\n    };\n}\n\nfunction integer(func, def) {\n\n    func = getDef(func, def);\n\n    if (typeof func === 'function') {\n        return function f() {\n            for (var _len2 = arguments.length, args = Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n                args[_key2] = arguments[_key2];\n            }\n\n            var n = parseInt(func.apply(this, args), 10);\n            return n != n ? 0 : n;\n        };\n    }\n\n    func = parseInt(func, 10);\n\n    return func != func ? function () {\n        return 0;\n    } : function () {\n        return func;\n    };\n}\n\nfunction string(func, def) {\n\n    func = getDef(func, def);\n\n    if (typeof func === 'function') {\n        return function f() {\n            for (var _len3 = arguments.length, args = Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n                args[_key3] = arguments[_key3];\n            }\n\n            return '' + func.apply(this, args);\n        };\n    }\n\n    func = '' + func;\n\n    return function () {\n        return func;\n    };\n}\n\nexport { boolean, integer, string };\n//# sourceMappingURL=bundle.es.js.map\n", "var prefix = ['webkit', 'moz', 'ms', 'o'];\n\nexport var requestAnimationFrame = function () {\n\n  for (var i = 0, limit = prefix.length; i < limit && !window.requestAnimationFrame; ++i) {\n    window.requestAnimationFrame = window[prefix[i] + 'RequestAnimationFrame'];\n  }\n\n  if (!window.requestAnimationFrame) {\n    (function () {\n      var lastTime = 0;\n\n      window.requestAnimationFrame = function (callback) {\n        var now = new Date().getTime();\n        var ttc = Math.max(0, 16 - now - lastTime);\n        var timer = window.setTimeout(function () {\n          return callback(now + ttc);\n        }, ttc);\n\n        lastTime = now + ttc;\n\n        return timer;\n      };\n    })();\n  }\n\n  return window.requestAnimationFrame.bind(window);\n}();\n\nexport var cancelAnimationFrame = function () {\n\n  for (var i = 0, limit = prefix.length; i < limit && !window.cancelAnimationFrame; ++i) {\n    window.cancelAnimationFrame = window[prefix[i] + 'CancelAnimationFrame'] || window[prefix[i] + 'CancelRequestAnimationFrame'];\n  }\n\n  if (!window.cancelAnimationFrame) {\n    window.cancelAnimationFrame = function (timer) {\n      window.clearTimeout(timer);\n    };\n  }\n\n  return window.cancelAnimationFrame.bind(window);\n}();\n", "// Production steps of ECMA-262, Edition 6, 22.1.2.1\n// Reference: http://www.ecma-international.org/ecma-262/6.0/#sec-array.from\nmodule.exports = (function() {\n  var isCallable = function(fn) {\n    return typeof fn === 'function';\n  };\n  var toInteger = function (value) {\n    var number = Number(value);\n    if (isNaN(number)) { return 0; }\n    if (number === 0 || !isFinite(number)) { return number; }\n    return (number > 0 ? 1 : -1) * Math.floor(Math.abs(number));\n  };\n  var maxSafeInteger = Math.pow(2, 53) - 1;\n  var toLength = function (value) {\n    var len = toInteger(value);\n    return Math.min(Math.max(len, 0), maxSafeInteger);\n  };\n  var iteratorProp = function(value) {\n    if(value != null) {\n      if(['string','number','boolean','symbol'].indexOf(typeof value) > -1){\n        return Symbol.iterator;\n      } else if (\n        (typeof Symbol !== 'undefined') &&\n        ('iterator' in Symbol) &&\n        (Symbol.iterator in value)\n      ) {\n        return Symbol.iterator;\n      }\n      // Support \"@@iterator\" placeholder, Gecko 27 to Gecko 35\n      else if ('@@iterator' in value) {\n        return '@@iterator';\n      }\n    }\n  };\n  var getMethod = function(O, P) {\n    // Assert: IsPropertyKey(P) is true.\n    if (O != null && P != null) {\n      // Let func be GetV(O, P).\n      var func = O[P];\n      // ReturnIfAbrupt(func).\n      // If func is either undefined or null, return undefined.\n      if(func == null) {\n        return void 0;\n      }\n      // If IsCallable(func) is false, throw a TypeError exception.\n      if (!isCallable(func)) {\n        throw new TypeError(func + ' is not a function');\n      }\n      return func;\n    }\n  };\n  var iteratorStep = function(iterator) {\n    // Let result be IteratorNext(iterator).\n    // ReturnIfAbrupt(result).\n    var result = iterator.next();\n    // Let done be IteratorComplete(result).\n    // ReturnIfAbrupt(done).\n    var done = Boolean(result.done);\n    // If done is true, return false.\n    if(done) {\n      return false;\n    }\n    // Return result.\n    return result;\n  };\n\n  // The length property of the from method is 1.\n  return function from(items /*, mapFn, thisArg */ ) {\n    'use strict';\n\n    // 1. Let C be the this value.\n    var C = this;\n\n    // 2. If mapfn is undefined, let mapping be false.\n    var mapFn = arguments.length > 1 ? arguments[1] : void 0;\n\n    var T;\n    if (typeof mapFn !== 'undefined') {\n      // 3. else\n      //   a. If IsCallable(mapfn) is false, throw a TypeError exception.\n      if (!isCallable(mapFn)) {\n        throw new TypeError(\n          'Array.from: when provided, the second argument must be a function'\n        );\n      }\n\n      //   b. If thisArg was supplied, let T be thisArg; else let T\n      //      be undefined.\n      if (arguments.length > 2) {\n        T = arguments[2];\n      }\n      //   c. Let mapping be true (implied by mapFn)\n    }\n\n    var A, k;\n\n    // 4. Let usingIterator be GetMethod(items, @@iterator).\n    // 5. ReturnIfAbrupt(usingIterator).\n    var usingIterator = getMethod(items, iteratorProp(items));\n\n    // 6. If usingIterator is not undefined, then\n    if (usingIterator !== void 0) {\n      // a. If IsConstructor(C) is true, then\n      //   i. Let A be the result of calling the [[Construct]]\n      //      internal method of C with an empty argument list.\n      // b. Else,\n      //   i. Let A be the result of the abstract operation ArrayCreate\n      //      with argument 0.\n      // c. ReturnIfAbrupt(A).\n      A = isCallable(C) ? Object(new C()) : [];\n\n      // d. Let iterator be GetIterator(items, usingIterator).\n      var iterator = usingIterator.call(items);\n\n      // e. ReturnIfAbrupt(iterator).\n      if (iterator == null) {\n        throw new TypeError(\n          'Array.from requires an array-like or iterable object'\n        );\n      }\n\n      // f. Let k be 0.\n      k = 0;\n\n      // g. Repeat\n      var next, nextValue;\n      while (true) {\n        // i. Let Pk be ToString(k).\n        // ii. Let next be IteratorStep(iterator).\n        // iii. ReturnIfAbrupt(next).\n        next = iteratorStep(iterator);\n\n        // iv. If next is false, then\n        if (!next) {\n\n          // 1. Let setStatus be Set(A, \"length\", k, true).\n          // 2. ReturnIfAbrupt(setStatus).\n          A.length = k;\n\n          // 3. Return A.\n          return A;\n        }\n        // v. Let nextValue be IteratorValue(next).\n        // vi. ReturnIfAbrupt(nextValue)\n        nextValue = next.value;\n\n        // vii. If mapping is true, then\n        //   1. Let mappedValue be Call(mapfn, T, «nextValue, k»).\n        //   2. If mappedValue is an abrupt completion, return\n        //      IteratorClose(iterator, mappedValue).\n        //   3. Let mappedValue be mappedValue.[[value]].\n        // viii. Else, let mappedValue be nextValue.\n        // ix.  Let defineStatus be the result of\n        //      CreateDataPropertyOrThrow(A, Pk, mappedValue).\n        // x. [TODO] If defineStatus is an abrupt completion, return\n        //    IteratorClose(iterator, defineStatus).\n        if (mapFn) {\n          A[k] = mapFn.call(T, nextValue, k);\n        }\n        else {\n          A[k] = nextValue;\n        }\n        // xi. Increase k by 1.\n        k++;\n      }\n      // 7. Assert: items is not an Iterable so assume it is\n      //    an array-like object.\n    } else {\n\n      // 8. Let arrayLike be ToObject(items).\n      var arrayLike = Object(items);\n\n      // 9. ReturnIfAbrupt(items).\n      if (items == null) {\n        throw new TypeError(\n          'Array.from requires an array-like object - not null or undefined'\n        );\n      }\n\n      // 10. Let len be ToLength(Get(arrayLike, \"length\")).\n      // 11. ReturnIfAbrupt(len).\n      var len = toLength(arrayLike.length);\n\n      // 12. If IsConstructor(C) is true, then\n      //     a. Let A be Construct(C, «len»).\n      // 13. Else\n      //     a. Let A be ArrayCreate(len).\n      // 14. ReturnIfAbrupt(A).\n      A = isCallable(C) ? Object(new C(len)) : new Array(len);\n\n      // 15. Let k be 0.\n      k = 0;\n      // 16. Repeat, while k < len… (also steps a - h)\n      var kValue;\n      while (k < len) {\n        kValue = arrayLike[k];\n        if (mapFn) {\n          A[k] = mapFn.call(T, kValue, k);\n        }\n        else {\n          A[k] = kValue;\n        }\n        k++;\n      }\n      // 17. Let setStatus be Set(A, \"length\", len, true).\n      // 18. ReturnIfAbrupt(setStatus).\n      A.length = len;\n      // 19. Return A.\n    }\n    return A;\n  };\n})();\n", "module.exports = (typeof Array.from === 'function' ?\n  Array.from :\n  require('./polyfill')\n);\n", "\n/**\n * isArray\n */\n\nvar isArray = Array.isArray;\n\n/**\n * toString\n */\n\nvar str = Object.prototype.toString;\n\n/**\n * Whether or not the given `val`\n * is an array.\n *\n * example:\n *\n *        isArray([]);\n *        // > true\n *        isArray(arguments);\n *        // > false\n *        isArray('');\n *        // > false\n *\n * @param {mixed} val\n * @return {bool}\n */\n\nmodule.exports = isArray || function (val) {\n  return !! val && '[object Array]' == str.call(val);\n};\n", "var _typeof = typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\" ? function (obj) { return typeof obj; } : function (obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol ? \"symbol\" : typeof obj; };\n\n/**\n * Returns `true` if provided input is Element.\n * @name isElement\n * @param {*} [input]\n * @returns {boolean}\n */\nexport default function (input) {\n  return input != null && (typeof input === 'undefined' ? 'undefined' : _typeof(input)) === 'object' && input.nodeType === 1 && _typeof(input.style) === 'object' && _typeof(input.ownerDocument) === 'object';\n}", "import arrayFrom from 'array-from';\nimport isArray from 'is-array';\nimport isElement from 'iselement';\n\nvar _typeof = typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\" ? function (obj) { return typeof obj; } : function (obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol ? \"symbol\" : typeof obj; };\n\n/**\n * Returns `true` if provided input is Element.\n * @name isElement\n * @param {*} [input]\n * @returns {boolean}\n */\nvar isElement$1 = function (input) {\n  return input != null && (typeof input === 'undefined' ? 'undefined' : _typeof(input)) === 'object' && input.nodeType === 1 && _typeof(input.style) === 'object' && _typeof(input.ownerDocument) === 'object';\n};\n\nfunction select(selector){\n    if(typeof selector === 'string'){\n        try{\n            return document.querySelector(selector);\n        }catch(e){\n            throw e;\n        }\n    }else if(isElement(selector)){\n        return selector;\n    }\n}\n\nfunction selectAll(selector){\n    if(typeof selector === 'string'){\n        return Array.prototype.slice.apply(\n            document.querySelectorAll(selector)\n        );\n    }else if(isArray(selector)){\n        return selector.map(select);\n    }else if('length' in selector){\n        return arrayFrom(selector).map(select);\n    }\n}\n\nfunction indexOfElement(elements, element){\n    element = resolveElement(element, true);\n    if(!isElement$1(element)) { return -1; }\n    for(var i=0; i<elements.length; i++){\n        if(elements[i] === element){\n            return i;\n        }\n    }\n    return -1;\n}\n\nfunction hasElement(elements, element){\n    return -1 !== indexOfElement(elements, element);\n}\n\nfunction domListOf(arr){\n\n    if(!arr) { return []; }\n\n    try{\n        if(typeof arr === 'string'){\n            return arrayFrom(document.querySelectorAll(arr));\n        }else if(isArray(arr)){\n            return arr.map(resolveElement);\n        }else{\n            if(typeof arr.length === 'undefined'){\n                return [resolveElement(arr)];\n            }\n\n            return arrayFrom(arr, resolveElement);\n\n        }\n    }catch(e){\n        throw new Error(e);\n    }\n\n}\n\nfunction concatElementLists(){\n    var lists = [], len = arguments.length;\n    while ( len-- ) lists[ len ] = arguments[ len ];\n\n    return lists.reduce(function (last, list){\n        return list.length ? last : last.concat(domListOf(list));\n    }, []);\n}\n\nfunction pushElements(elements, toAdd){\n\n    for(var i=0; i<toAdd.length; i++){\n        if(!hasElement(elements, toAdd[i]))\n            { elements.push(toAdd[i]); }\n    }\n\n    return toAdd;\n}\n\nfunction addElements(elements){\n    var toAdd = [], len = arguments.length - 1;\n    while ( len-- > 0 ) toAdd[ len ] = arguments[ len + 1 ];\n\n    toAdd = toAdd.map(resolveElement);\n    return pushElements(elements, toAdd);\n}\n\nfunction removeElements(elements){\n    var toRemove = [], len = arguments.length - 1;\n    while ( len-- > 0 ) toRemove[ len ] = arguments[ len + 1 ];\n\n    return toRemove.map(resolveElement).reduce(function (last, e){\n\n        var index = indexOfElement(elements, e);\n\n        if(index !== -1)\n            { return last.concat(elements.splice(index, 1)); }\n        return last;\n    }, []);\n}\n\nfunction resolveElement(element, noThrow){\n    if(typeof element === 'string'){\n        try{\n            return document.querySelector(element);\n        }catch(e){\n            throw e;\n        }\n\n    }\n\n    if(!isElement$1(element) && !noThrow){\n        throw new TypeError((element + \" is not a DOM element.\"));\n    }\n    return element;\n}\n\nexport { indexOfElement, hasElement, domListOf, concatElementLists, addElements, removeElements, resolveElement, select, selectAll };\n//# sourceMappingURL=bundle.es.js.map\n", "import { boolean } from 'type-func';\n\nfunction createPointCB(object, options) {\n\n    // A persistent object (as opposed to returned object) is used to save memory\n    // This is good to prevent layout thrashing, or for games, and such\n\n    // NOTE\n    // This uses IE fixes which should be OK to remove some day. :)\n    // Some speed will be gained by removal of these.\n\n    // pointCB should be saved in a variable on return\n    // This allows the usage of element.removeEventListener\n\n    options = options || {};\n\n    var allowUpdate = boolean(options.allowUpdate, true);\n\n    /*if(typeof options.allowUpdate === 'function'){\n        allowUpdate = options.allowUpdate;\n    }else{\n        allowUpdate = function(){return true;};\n    }*/\n\n    return function pointCB(event) {\n\n        event = event || window.event; // IE-ism\n        object.target = event.target || event.srcElement || event.originalTarget;\n        object.element = this;\n        object.type = event.type;\n\n        if (!allowUpdate(event)) {\n            return;\n        }\n\n        // Support touch\n        // http://www.creativebloq.com/javascript/make-your-site-work-touch-devices-51411644\n\n        if (event.targetTouches) {\n            object.x = event.targetTouches[0].clientX;\n            object.y = event.targetTouches[0].clientY;\n            object.pageX = event.targetTouches[0].pageX;\n            object.pageY = event.targetTouches[0].pageY;\n            object.screenX = event.targetTouches[0].screenX;\n            object.screenY = event.targetTouches[0].screenY;\n        } else {\n\n            // If pageX/Y aren't available and clientX/Y are,\n            // calculate pageX/Y - logic taken from jQuery.\n            // (This is to support old IE)\n            // NOTE Hopefully this can be removed soon.\n\n            if (event.pageX === null && event.clientX !== null) {\n                var eventDoc = event.target && event.target.ownerDocument || document;\n                var doc = eventDoc.documentElement;\n                var body = eventDoc.body;\n\n                object.pageX = event.clientX + (doc && doc.scrollLeft || body && body.scrollLeft || 0) - (doc && doc.clientLeft || body && body.clientLeft || 0);\n                object.pageY = event.clientY + (doc && doc.scrollTop || body && body.scrollTop || 0) - (doc && doc.clientTop || body && body.clientTop || 0);\n            } else {\n                object.pageX = event.pageX;\n                object.pageY = event.pageY;\n            }\n\n            // pageX, and pageY change with page scroll\n            // so we're not going to use those for x, and y.\n            // NOTE Most browsers also alias clientX/Y with x/y\n            // so that's something to consider down the road.\n\n            object.x = event.clientX;\n            object.y = event.clientY;\n\n            object.screenX = event.screenX;\n            object.screenY = event.screenY;\n        }\n\n        object.clientX = object.x;\n        object.clientY = object.y;\n    };\n\n    //NOTE Remember accessibility, Aria roles, and labels.\n}\n\n/*\ngit remote add origin https://github.com/hollowdoor/create_point_cb.git\ngit push -u origin master\n*/\n\nexport default createPointCB;\n//# sourceMappingURL=bundle.es.js.map\n", "import createPointCb from 'create-point-cb';\n\nfunction createWindowRect() {\n    var props = {\n        top: { value: 0, enumerable: true },\n        left: { value: 0, enumerable: true },\n        right: { value: window.innerWidth, enumerable: true },\n        bottom: { value: window.innerHeight, enumerable: true },\n        width: { value: window.innerWidth, enumerable: true },\n        height: { value: window.innerHeight, enumerable: true },\n        x: { value: 0, enumerable: true },\n        y: { value: 0, enumerable: true }\n    };\n\n    if (Object.create) {\n        return Object.create({}, props);\n    } else {\n        var rect = {};\n        Object.defineProperties(rect, props);\n        return rect;\n    }\n}\n\nfunction getClientRect(el) {\n    if (el === window) {\n        return createWindowRect();\n    } else {\n        try {\n            var rect = el.getBoundingClientRect();\n            if (rect.x === undefined) {\n                rect.x = rect.left;\n                rect.y = rect.top;\n            }\n            return rect;\n        } catch (e) {\n            throw new TypeError(\"Can't call getBoundingClientRect on \" + el);\n        }\n    }\n}\n\nfunction pointInside(point, el) {\n    var rect = getClientRect(el);\n    return point.y > rect.top && point.y < rect.bottom && point.x > rect.left && point.x < rect.right;\n}\n\nexport { createPointCb as createPointCB, getClientRect, pointInside };\n//# sourceMappingURL=bundle.es.js.map\n", "var objectCreate = void 0;\nif (typeof Object.create != 'function') {\n  objectCreate = function (undefined) {\n    var Temp = function Temp() {};\n    return function (prototype, propertiesObject) {\n      if (prototype !== Object(prototype) && prototype !== null) {\n        throw TypeError('Argument must be an object, or null');\n      }\n      Temp.prototype = prototype || {};\n      var result = new Temp();\n      Temp.prototype = null;\n      if (propertiesObject !== undefined) {\n        Object.defineProperties(result, propertiesObject);\n      }\n\n      // to imitate the case of Object.create(null)\n      if (prototype === null) {\n        result.__proto__ = null;\n      }\n      return result;\n    };\n  }();\n} else {\n  objectCreate = Object.create;\n}\n\nvar objectCreate$1 = objectCreate;\n\nvar mouseEventProps = ['altKey', 'button', 'buttons', 'clientX', 'clientY', 'ctrlKey', 'metaKey', 'movementX', 'movementY', 'offsetX', 'offsetY', 'pageX', 'pageY', 'region', 'relatedTarget', 'screenX', 'screenY', 'shiftKey', 'which', 'x', 'y'];\n\nfunction createDispatcher(element) {\n\n    var defaultSettings = {\n        screenX: 0,\n        screenY: 0,\n        clientX: 0,\n        clientY: 0,\n        ctrlKey: false,\n        shiftKey: false,\n        altKey: false,\n        metaKey: false,\n        button: 0,\n        buttons: 1,\n        relatedTarget: null,\n        region: null\n    };\n\n    if (element !== undefined) {\n        element.addEventListener('mousemove', onMove);\n    }\n\n    function onMove(e) {\n        for (var i = 0; i < mouseEventProps.length; i++) {\n            defaultSettings[mouseEventProps[i]] = e[mouseEventProps[i]];\n        }\n    }\n\n    var dispatch = function () {\n        if (MouseEvent) {\n            return function m1(element, initMove, data) {\n                var evt = new MouseEvent('mousemove', createMoveInit(defaultSettings, initMove));\n\n                //evt.dispatched = 'mousemove';\n                setSpecial(evt, data);\n\n                return element.dispatchEvent(evt);\n            };\n        } else if (typeof document.createEvent === 'function') {\n            return function m2(element, initMove, data) {\n                var settings = createMoveInit(defaultSettings, initMove);\n                var evt = document.createEvent('MouseEvents');\n\n                evt.initMouseEvent(\"mousemove\", true, //can bubble\n                true, //cancelable\n                window, //view\n                0, //detail\n                settings.screenX, //0, //screenX\n                settings.screenY, //0, //screenY\n                settings.clientX, //80, //clientX\n                settings.clientY, //20, //clientY\n                settings.ctrlKey, //false, //ctrlKey\n                settings.altKey, //false, //altKey\n                settings.shiftKey, //false, //shiftKey\n                settings.metaKey, //false, //metaKey\n                settings.button, //0, //button\n                settings.relatedTarget //null //relatedTarget\n                );\n\n                //evt.dispatched = 'mousemove';\n                setSpecial(evt, data);\n\n                return element.dispatchEvent(evt);\n            };\n        } else if (typeof document.createEventObject === 'function') {\n            return function m3(element, initMove, data) {\n                var evt = document.createEventObject();\n                var settings = createMoveInit(defaultSettings, initMove);\n                for (var name in settings) {\n                    evt[name] = settings[name];\n                }\n\n                //evt.dispatched = 'mousemove';\n                setSpecial(evt, data);\n\n                return element.dispatchEvent(evt);\n            };\n        }\n    }();\n\n    function destroy() {\n        if (element) element.removeEventListener('mousemove', onMove, false);\n        defaultSettings = null;\n    }\n\n    return {\n        destroy: destroy,\n        dispatch: dispatch\n    };\n}\n\nfunction createMoveInit(defaultSettings, initMove) {\n    initMove = initMove || {};\n    var settings = objectCreate$1(defaultSettings);\n    for (var i = 0; i < mouseEventProps.length; i++) {\n        if (initMove[mouseEventProps[i]] !== undefined) settings[mouseEventProps[i]] = initMove[mouseEventProps[i]];\n    }\n\n    return settings;\n}\n\nfunction setSpecial(e, data) {\n    console.log('data ', data);\n    e.data = data || {};\n    e.dispatched = 'mousemove';\n}\n\n/*\nhttp://marcgrabanski.com/simulating-mouse-click-events-in-javascript/\n*/\n\nexport default createDispatcher;\n//# sourceMappingURL=bundle.es.js.map\n", "import {boolean} from 'type-func';\r\nimport {\r\n    requestAnimation<PERSON>rame as requestFrame,\r\n    cancelAnimation<PERSON>rame as cancelFrame\r\n} from 'animation-frame-polyfill';\r\nimport {\r\n    hasElement,\r\n    addElements,\r\n    removeElements\r\n} from 'dom-set';\r\n\r\nimport {\r\n    createPointCB,\r\n    getClientRect as getRect,\r\n    pointInside\r\n} from 'dom-plane';\r\n\r\nimport mousemoveDispatcher from 'dom-mousemove-dispatcher';\r\n\r\nfunction AutoScroller(elements, options = {}){\r\n    const self = this;\r\n    let maxSpeed = 4, scrolling = false;\r\n\r\n    this.margin = options.margin || -1;\r\n    //this.scrolling = false;\r\n    this.scrollWhenOutside = options.scrollWhenOutside || false;\r\n\r\n    let point = {},\r\n        pointCB = createPointCB(point),\r\n        dispatcher = mousemoveDispatcher(),\r\n        down = false;\r\n\r\n    window.addEventListener('mousemove', pointCB, false);\r\n    window.addEventListener('touchmove', pointCB, false);\r\n\r\n    if(!isNaN(options.maxSpeed)){\r\n        maxSpeed = options.maxSpeed;\r\n    }\r\n\r\n    this.autoScroll = boolean(options.autoScroll);\r\n    this.syncMove = boolean(options.syncMove, false);\r\n\r\n    this.destroy = function(forceCleanAnimation) {\r\n        window.removeEventListener('mousemove', pointCB, false);\r\n        window.removeEventListener('touchmove', pointCB, false);\r\n        window.removeEventListener('mousedown', onDown, false);\r\n        window.removeEventListener('touchstart', onDown, false);\r\n        window.removeEventListener('mouseup', onUp, false);\r\n        window.removeEventListener('touchend', onUp, false);\r\n        window.removeEventListener('pointerup', onUp, false);\r\n        window.removeEventListener('mouseleave', onMouseOut, false);\r\n\r\n        window.removeEventListener('mousemove', onMove, false);\r\n        window.removeEventListener('touchmove', onMove, false);\r\n\r\n        window.removeEventListener('scroll', setScroll, true);\r\n        elements = [];\r\n        if(forceCleanAnimation){\r\n          cleanAnimation();\r\n        }\r\n    };\r\n\r\n    this.add = function(...element){\r\n        addElements(elements, ...element);\r\n        return this;\r\n    };\r\n\r\n    this.remove = function(...element){\r\n        return removeElements(elements, ...element);\r\n    };\r\n\r\n    let hasWindow = null, windowAnimationFrame;\r\n\r\n    if(Object.prototype.toString.call(elements) !== '[object Array]'){\r\n        elements = [elements];\r\n    }\r\n\r\n    (function(temp){\r\n        elements = [];\r\n        temp.forEach(function(element){\r\n            if(element === window){\r\n                hasWindow = window;\r\n            }else{\r\n                self.add(element);\r\n            }\r\n        })\r\n    }(elements));\r\n\r\n    Object.defineProperties(this, {\r\n        down: {\r\n            get: function(){ return down; }\r\n        },\r\n        maxSpeed: {\r\n            get: function(){ return maxSpeed; }\r\n        },\r\n        point: {\r\n            get: function(){ return point; }\r\n        },\r\n        scrolling: {\r\n            get: function(){ return scrolling; }\r\n        }\r\n    });\r\n\r\n    let n = 0, current = null, animationFrame;\r\n\r\n    window.addEventListener('mousedown', onDown, false);\r\n    window.addEventListener('touchstart', onDown, false);\r\n    window.addEventListener('mouseup', onUp, false);\r\n    window.addEventListener('touchend', onUp, false);\r\n\r\n    /*\r\n    IE does not trigger mouseup event when scrolling.\r\n    It is a known issue that Microsoft won't fix.\r\n    https://connect.microsoft.com/IE/feedback/details/783058/scrollbar-trigger-mousedown-but-not-mouseup\r\n    IE supports pointer events instead\r\n    */\r\n    window.addEventListener('pointerup', onUp, false);\r\n\r\n    window.addEventListener('mousemove', onMove, false);\r\n    window.addEventListener('touchmove', onMove, false);\r\n\r\n    window.addEventListener('mouseleave', onMouseOut, false);\r\n\r\n    window.addEventListener('scroll', setScroll, true);\r\n\r\n    function setScroll(e){\r\n\r\n        for(let i=0; i<elements.length; i++){\r\n            if(elements[i] === e.target){\r\n                scrolling = true;\r\n                break;\r\n            }\r\n        }\r\n\r\n        if(scrolling){\r\n            requestFrame(()=>scrolling = false)\r\n        }\r\n    }\r\n\r\n    function onDown(){\r\n        down = true;\r\n    }\r\n\r\n    function onUp(){\r\n        down = false;\r\n        cleanAnimation();\r\n    }\r\n    function cleanAnimation(){\r\n      cancelFrame(animationFrame);\r\n      cancelFrame(windowAnimationFrame);\r\n    }\r\n    function onMouseOut(){\r\n        down = false;\r\n    }\r\n\r\n    function getTarget(target){\r\n        if(!target){\r\n            return null;\r\n        }\r\n\r\n        if(current === target){\r\n            return target;\r\n        }\r\n\r\n        if(hasElement(elements, target)){\r\n            return target;\r\n        }\r\n\r\n        while(target = target.parentNode){\r\n            if(hasElement(elements, target)){\r\n                return target;\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    function getElementUnderPoint(){\r\n        let underPoint = null;\r\n\r\n        for(var i=0; i<elements.length; i++){\r\n            if(inside(point, elements[i])){\r\n                underPoint = elements[i];\r\n            }\r\n        }\r\n\r\n        return underPoint;\r\n    }\r\n\r\n\r\n    function onMove(event){\r\n\r\n        if(!self.autoScroll()) return;\r\n\r\n        if(event['dispatched']){ return; }\r\n\r\n        let target = event.target, body = document.body;\r\n\r\n        if(current && !inside(point, current)){\r\n            if(!self.scrollWhenOutside){\r\n                current = null;\r\n            }\r\n        }\r\n\r\n        if(target && target.parentNode === body){\r\n            //The special condition to improve speed.\r\n            target = getElementUnderPoint();\r\n        }else{\r\n            target = getTarget(target);\r\n\r\n            if(!target){\r\n                target = getElementUnderPoint();\r\n            }\r\n        }\r\n\r\n\r\n        if(target && target !== current){\r\n            current = target;\r\n        }\r\n\r\n        if(hasWindow){\r\n            cancelFrame(windowAnimationFrame);\r\n            windowAnimationFrame = requestFrame(scrollWindow);\r\n        }\r\n\r\n\r\n        if(!current){\r\n            return;\r\n        }\r\n\r\n        cancelFrame(animationFrame);\r\n        animationFrame = requestFrame(scrollTick);\r\n    }\r\n\r\n    function scrollWindow(){\r\n        autoScroll(hasWindow);\r\n\r\n        cancelFrame(windowAnimationFrame);\r\n        windowAnimationFrame = requestFrame(scrollWindow);\r\n    }\r\n\r\n    function scrollTick(){\r\n\r\n        if(!current){\r\n            return;\r\n        }\r\n\r\n        autoScroll(current);\r\n\r\n        cancelFrame(animationFrame);\r\n        animationFrame = requestFrame(scrollTick);\r\n\r\n    }\r\n\r\n\r\n    function autoScroll(el){\r\n        let rect = getRect(el), scrollx, scrolly;\r\n\r\n        if(point.x < rect.left + self.margin){\r\n            scrollx = Math.floor(\r\n                Math.max(-1, (point.x - rect.left) / self.margin - 1) * self.maxSpeed\r\n            );\r\n        }else if(point.x > rect.right - self.margin){\r\n            scrollx = Math.ceil(\r\n                Math.min(1, (point.x - rect.right) / self.margin + 1) * self.maxSpeed\r\n            );\r\n        }else{\r\n            scrollx = 0;\r\n        }\r\n\r\n        if(point.y < rect.top + self.margin){\r\n            scrolly = Math.floor(\r\n                Math.max(-1, (point.y - rect.top) / self.margin - 1) * self.maxSpeed\r\n            );\r\n        }else if(point.y > rect.bottom - self.margin){\r\n            scrolly = Math.ceil(\r\n                Math.min(1, (point.y - rect.bottom) / self.margin + 1) * self.maxSpeed\r\n            );\r\n        }else{\r\n            scrolly = 0;\r\n        }\r\n\r\n        if(self.syncMove()){\r\n            /*\r\n            Notes about mousemove event dispatch.\r\n            screen(X/Y) should need to be updated.\r\n            Some other properties might need to be set.\r\n            Keep the syncMove option default false until all inconsistencies are taken care of.\r\n            */\r\n            dispatcher.dispatch(el, {\r\n                pageX: point.pageX + scrollx,\r\n                pageY: point.pageY + scrolly,\r\n                clientX: point.x + scrollx,\r\n                clientY: point.y + scrolly\r\n            });\r\n        }\r\n\r\n        setTimeout(()=>{\r\n\r\n            if(scrolly){\r\n                scrollY(el, scrolly);\r\n            }\r\n\r\n            if(scrollx){\r\n                scrollX(el, scrollx);\r\n            }\r\n\r\n        });\r\n    }\r\n\r\n    function scrollY(el, amount){\r\n        if(el === window){\r\n            window.scrollTo(el.pageXOffset, el.pageYOffset + amount);\r\n        }else{\r\n            el.scrollTop += amount;\r\n        }\r\n    }\r\n\r\n    function scrollX(el, amount){\r\n        if(el === window){\r\n            window.scrollTo(el.pageXOffset + amount, el.pageYOffset);\r\n        }else{\r\n            el.scrollLeft += amount;\r\n        }\r\n    }\r\n\r\n}\r\n\r\nexport default function AutoScrollerFactory(element, options){\r\n    return new AutoScroller(element, options);\r\n}\r\n\r\nfunction inside(point, el, rect){\r\n    if(!rect){\r\n        return pointInside(point, el);\r\n    }else{\r\n        return (point.y > rect.top && point.y < rect.bottom &&\r\n                point.x > rect.left && point.x < rect.right);\r\n    }\r\n}\r\n\r\n/*\r\ngit remote add origin https://github.com/hollowdoor/dom_autoscroller.git\r\ngit push -u origin master\r\n*/\r\n"], "names": ["arguments", "require$$0", "arrayFrom", "isArray", "index", "const", "let", "mousemove<PERSON><PERSON><PERSON><PERSON>er", "requestFrame", "cancelFrame", "getRect"], "mappings": ";;;AAAA,SAAS,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE;IAClB,IAAI,OAAO,CAAC,KAAK,WAAW,EAAE;QAC1B,OAAO,OAAO,CAAC,KAAK,WAAW,GAAG,CAAC,GAAG,CAAC,CAAC;KAC3C;;IAED,OAAO,CAAC,CAAC;CACZ;AACD,SAAS,OAAO,CAAC,IAAI,EAAE,GAAG,EAAE;;IAExB,IAAI,GAAG,MAAM,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;;IAEzB,IAAI,OAAO,IAAI,KAAK,UAAU,EAAE;QAC5B,OAAO,SAAS,CAAC,GAAG;;;YAChB,KAAK,IAAI,IAAI,GAAG,SAAS,CAAC,MAAM,EAAE,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,GAAG,CAAC,EAAE,IAAI,GAAG,IAAI,EAAE,IAAI,EAAE,EAAE;gBACjF,IAAI,CAAC,IAAI,CAAC,GAAGA,WAAS,CAAC,IAAI,CAAC,CAAC;aAChC;;YAED,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;SACnC,CAAC;KACL;;IAED,OAAO,CAAC,CAAC,IAAI,GAAG,YAAY;QACxB,OAAO,IAAI,CAAC;KACf,GAAG,YAAY;QACZ,OAAO,KAAK,CAAC;KAChB,CAAC;CACL,AAED,AAwBA,AAqBA,AAAoC;;ACzEpC,IAAI,MAAM,GAAG,CAAC,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC;;AAE1C,AAAO,IAAI,qBAAqB,GAAG,YAAY;;EAE7C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,KAAK,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,KAAK,IAAI,CAAC,MAAM,CAAC,qBAAqB,EAAE,EAAE,CAAC,EAAE;IACtF,MAAM,CAAC,qBAAqB,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,uBAAuB,CAAC,CAAC;GAC5E;;EAED,IAAI,CAAC,MAAM,CAAC,qBAAqB,EAAE;IACjC,CAAC,YAAY;MACX,IAAI,QAAQ,GAAG,CAAC,CAAC;;MAEjB,MAAM,CAAC,qBAAqB,GAAG,UAAU,QAAQ,EAAE;QACjD,IAAI,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,CAAC;QAC/B,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,GAAG,GAAG,GAAG,QAAQ,CAAC,CAAC;QAC3C,IAAI,KAAK,GAAG,MAAM,CAAC,UAAU,CAAC,YAAY;UACxC,OAAO,QAAQ,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;SAC5B,EAAE,GAAG,CAAC,CAAC;;QAER,QAAQ,GAAG,GAAG,GAAG,GAAG,CAAC;;QAErB,OAAO,KAAK,CAAC;OACd,CAAC;KACH,GAAG,CAAC;GACN;;EAED,OAAO,MAAM,CAAC,qBAAqB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;CAClD,EAAE,CAAC;;AAEJ,AAAO,IAAI,oBAAoB,GAAG,YAAY;;EAE5C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,KAAK,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,KAAK,IAAI,CAAC,MAAM,CAAC,oBAAoB,EAAE,EAAE,CAAC,EAAE;IACrF,MAAM,CAAC,oBAAoB,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,sBAAsB,CAAC,IAAI,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,6BAA6B,CAAC,CAAC;GAC/H;;EAED,IAAI,CAAC,MAAM,CAAC,oBAAoB,EAAE;IAChC,MAAM,CAAC,oBAAoB,GAAG,UAAU,KAAK,EAAE;MAC7C,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;KAC5B,CAAC;GACH;;EAED,OAAO,MAAM,CAAC,oBAAoB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;CACjD,EAAE,CAAC;;AC1CJ;;AAEA,YAAc,GAAG,CAAC,WAAW;EAC3B,IAAI,UAAU,GAAG,SAAS,EAAE,EAAE;IAC5B,OAAO,OAAO,EAAE,KAAK,UAAU,CAAC;GACjC,CAAC;EACF,IAAI,SAAS,GAAG,UAAU,KAAK,EAAE;IAC/B,IAAI,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;IAC3B,IAAI,KAAK,CAAC,MAAM,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC,EAAE;IAChC,IAAI,MAAM,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,EAAE,OAAO,MAAM,CAAC,EAAE;IACzD,OAAO,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC;GAC7D,CAAC;EACF,IAAI,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC;EACzC,IAAI,QAAQ,GAAG,UAAU,KAAK,EAAE;IAC9B,IAAI,GAAG,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC;IAC3B,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,cAAc,CAAC,CAAC;GACnD,CAAC;EACF,IAAI,YAAY,GAAG,SAAS,KAAK,EAAE;IACjC,GAAG,KAAK,IAAI,IAAI,EAAE;MAChB,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,OAAO,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;QACnE,OAAO,MAAM,CAAC,QAAQ,CAAC;OACxB,MAAM;QACL,CAAC,OAAO,MAAM,KAAK,WAAW;SAC7B,UAAU,IAAI,MAAM,CAAC;SACrB,MAAM,CAAC,QAAQ,IAAI,KAAK,CAAC;QAC1B;QACA,OAAO,MAAM,CAAC,QAAQ,CAAC;OACxB;;WAEI,IAAI,YAAY,IAAI,KAAK,EAAE;QAC9B,OAAO,YAAY,CAAC;OACrB;KACF;GACF,CAAC;EACF,IAAI,SAAS,GAAG,SAAS,CAAC,EAAE,CAAC,EAAE;;IAE7B,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,IAAI,EAAE;;MAE1B,IAAI,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;;;MAGhB,GAAG,IAAI,IAAI,IAAI,EAAE;QACf,OAAO,KAAK,CAAC,CAAC;OACf;;MAED,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;QACrB,MAAM,IAAI,SAAS,CAAC,IAAI,GAAG,oBAAoB,CAAC,CAAC;OAClD;MACD,OAAO,IAAI,CAAC;KACb;GACF,CAAC;EACF,IAAI,YAAY,GAAG,SAAS,QAAQ,EAAE;;;IAGpC,IAAI,MAAM,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC;;;IAG7B,IAAI,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;;IAEhC,GAAG,IAAI,EAAE;MACP,OAAO,KAAK,CAAC;KACd;;IAED,OAAO,MAAM,CAAC;GACf,CAAC;;;EAGF,OAAO,SAAS,IAAI,CAAC,KAAK,yBAAyB;IACjD,YAAY,CAAC;;;IAGb,IAAI,CAAC,GAAG,IAAI,CAAC;;;IAGb,IAAI,KAAK,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;;IAEzD,IAAI,CAAC,CAAC;IACN,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE;;;MAGhC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE;QACtB,MAAM,IAAI,SAAS;UACjB,mEAAmE;SACpE,CAAC;OACH;;;;MAID,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;QACxB,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;OAClB;;KAEF;;IAED,IAAI,CAAC,EAAE,CAAC,CAAC;;;;IAIT,IAAI,aAAa,GAAG,SAAS,CAAC,KAAK,EAAE,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC;;;IAG1D,IAAI,aAAa,KAAK,KAAK,CAAC,EAAE;;;;;;;;MAQ5B,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC;;;MAGzC,IAAI,QAAQ,GAAG,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;;;MAGzC,IAAI,QAAQ,IAAI,IAAI,EAAE;QACpB,MAAM,IAAI,SAAS;UACjB,sDAAsD;SACvD,CAAC;OACH;;;MAGD,CAAC,GAAG,CAAC,CAAC;;;MAGN,IAAI,IAAI,EAAE,SAAS,CAAC;MACpB,OAAO,IAAI,EAAE;;;;QAIX,IAAI,GAAG,YAAY,CAAC,QAAQ,CAAC,CAAC;;;QAG9B,IAAI,CAAC,IAAI,EAAE;;;;UAIT,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;;;UAGb,OAAO,CAAC,CAAC;SACV;;;QAGD,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC;;;;;;;;;;;;QAYvB,IAAI,KAAK,EAAE;UACT,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,SAAS,EAAE,CAAC,CAAC,CAAC;SACpC;aACI;UACH,CAAC,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC;SAClB;;QAED,CAAC,EAAE,CAAC;OACL;;;KAGF,MAAM;;;MAGL,IAAI,SAAS,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;;;MAG9B,IAAI,KAAK,IAAI,IAAI,EAAE;QACjB,MAAM,IAAI,SAAS;UACjB,kEAAkE;SACnE,CAAC;OACH;;;;MAID,IAAI,GAAG,GAAG,QAAQ,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;;;;;;;MAOrC,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC;;;MAGxD,CAAC,GAAG,CAAC,CAAC;;MAEN,IAAI,MAAM,CAAC;MACX,OAAO,CAAC,GAAG,GAAG,EAAE;QACd,MAAM,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;QACtB,IAAI,KAAK,EAAE;UACT,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC;SACjC;aACI;UACH,CAAC,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC;SACf;QACD,CAAC,EAAE,CAAC;OACL;;;MAGD,CAAC,CAAC,MAAM,GAAG,GAAG,CAAC;;KAEhB;IACD,OAAO,CAAC,CAAC;GACV,CAAC;CACH,GAAG,CAAC;;ACnNL,SAAc,IAAI,OAAO,KAAK,CAAC,IAAI,KAAK,UAAU;EAChD,KAAK,CAAC,IAAI;EACVC,QAAqB;CACtB,CAAC;;ACFF;;;;AAIA,IAAI,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;;;;;;AAM5B,IAAI,GAAG,GAAG,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC;;;;;;;;;;;;;;;;;;;AAmBpC,WAAc,GAAG,OAAO,IAAI,UAAU,GAAG,EAAE;EACzC,OAAO,CAAC,EAAE,GAAG,IAAI,gBAAgB,IAAI,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;CACpD,CAAC;;;;;;;GCxBF;;ACJA,IAAI,OAAO,GAAG,OAAO,MAAM,KAAK,UAAU,IAAI,OAAO,MAAM,CAAC,QAAQ,KAAK,QAAQ,GAAG,UAAU,GAAG,EAAE,EAAE,OAAO,OAAO,GAAG,CAAC,EAAE,GAAG,UAAU,GAAG,EAAE,EAAE,OAAO,GAAG,IAAI,OAAO,MAAM,KAAK,UAAU,IAAI,GAAG,CAAC,WAAW,KAAK,MAAM,GAAG,QAAQ,GAAG,OAAO,GAAG,CAAC,EAAE,CAAC;;;;;;;;AAQjP,IAAI,WAAW,GAAG,UAAU,KAAK,EAAE;EACjC,OAAO,KAAK,IAAI,IAAI,IAAI,CAAC,OAAO,KAAK,KAAK,WAAW,GAAG,WAAW,GAAG,OAAO,CAAC,KAAK,CAAC,MAAM,QAAQ,IAAI,KAAK,CAAC,QAAQ,KAAK,CAAC,IAAI,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,QAAQ,IAAI,OAAO,CAAC,KAAK,CAAC,aAAa,CAAC,KAAK,QAAQ,CAAC;CAC9M,CAAC;;AAEF,AAYA,AAYA,SAAS,cAAc,CAAC,QAAQ,EAAE,OAAO,CAAC;IACtC,OAAO,GAAG,cAAc,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;IACxC,GAAG,CAAC,WAAW,CAAC,OAAO,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE;IACxC,IAAI,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC;QAChC,GAAG,QAAQ,CAAC,CAAC,CAAC,KAAK,OAAO,CAAC;YACvB,OAAO,CAAC,CAAC;SACZ;KACJ;IACD,OAAO,CAAC,CAAC,CAAC;CACb;;AAED,SAAS,UAAU,CAAC,QAAQ,EAAE,OAAO,CAAC;IAClC,OAAO,CAAC,CAAC,KAAK,cAAc,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;CACnD;;AAED,SAAS,SAAS,CAAC,GAAG,CAAC;;IAEnB,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,EAAE,CAAC,EAAE;;IAEvB,GAAG;QACC,GAAG,OAAO,GAAG,KAAK,QAAQ,CAAC;YACvB,OAAOC,KAAS,CAAC,QAAQ,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC;SACpD,KAAK,GAAGC,OAAO,CAAC,GAAG,CAAC,CAAC;YAClB,OAAO,GAAG,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;SAClC,IAAI;YACD,GAAG,OAAO,GAAG,CAAC,MAAM,KAAK,WAAW,CAAC;gBACjC,OAAO,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC;aAChC;;YAED,OAAOD,KAAS,CAAC,GAAG,EAAE,cAAc,CAAC,CAAC;;SAEzC;KACJ,MAAM,CAAC,CAAC;QACL,MAAM,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC;KACtB;;CAEJ;;AAED,AASA,SAAS,YAAY,CAAC,QAAQ,EAAE,KAAK,CAAC;;IAElC,IAAI,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC;QAC7B,GAAG,CAAC,UAAU,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;YAC9B,EAAE,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;KACnC;;IAED,OAAO,KAAK,CAAC;CAChB;;AAED,SAAS,WAAW,CAAC,QAAQ,CAAC;;;IAC1B,IAAI,KAAK,GAAG,EAAE,EAAE,GAAG,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;IAC3C,QAAQ,GAAG,EAAE,GAAG,CAAC,GAAG,EAAA,KAAK,EAAE,GAAG,EAAE,GAAGF,WAAS,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC,EAAA;;IAExD,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;IAClC,OAAO,YAAY,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;CACxC;;AAED,SAAS,cAAc,CAAC,QAAQ,CAAC;;;IAC7B,IAAI,QAAQ,GAAG,EAAE,EAAE,GAAG,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;IAC9C,QAAQ,GAAG,EAAE,GAAG,CAAC,GAAG,EAAA,QAAQ,EAAE,GAAG,EAAE,GAAGA,WAAS,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC,EAAA;;IAE3D,OAAO,QAAQ,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC,MAAM,CAAC,UAAU,IAAI,EAAE,CAAC,CAAC;;QAEzD,IAAII,QAAK,GAAG,cAAc,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;;QAExC,GAAGA,QAAK,KAAK,CAAC,CAAC;YACX,EAAE,OAAO,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAACA,QAAK,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;QACtD,OAAO,IAAI,CAAC;KACf,EAAE,EAAE,CAAC,CAAC;CACV;;AAED,SAAS,cAAc,CAAC,OAAO,EAAE,OAAO,CAAC;IACrC,GAAG,OAAO,OAAO,KAAK,QAAQ,CAAC;QAC3B,GAAG;YACC,OAAO,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;SAC1C,MAAM,CAAC,CAAC;YACL,MAAM,CAAC,CAAC;SACX;;KAEJ;;IAED,GAAG,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC;QACjC,MAAM,IAAI,SAAS,EAAE,OAAO,GAAG,wBAAwB,EAAE,CAAC;KAC7D;IACD,OAAO,OAAO,CAAC;CAClB,AAED,AAAqI;;ACrIrI,SAAS,aAAa,CAAC,MAAM,EAAE,OAAO,EAAE;;;;;;;;;;;;IAYpC,OAAO,GAAG,OAAO,IAAI,EAAE,CAAC;;IAExB,IAAI,WAAW,GAAG,OAAO,CAAC,OAAO,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;;;;;;;;IAQrD,OAAO,SAAS,OAAO,CAAC,KAAK,EAAE;;QAE3B,KAAK,GAAG,KAAK,IAAI,MAAM,CAAC,KAAK,CAAC;QAC9B,MAAM,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,UAAU,IAAI,KAAK,CAAC,cAAc,CAAC;QACzE,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC;QACtB,MAAM,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;;QAEzB,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE;YACrB,OAAO;SACV;;;;;QAKD,IAAI,KAAK,CAAC,aAAa,EAAE;YACrB,MAAM,CAAC,CAAC,GAAG,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;YAC1C,MAAM,CAAC,CAAC,GAAG,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;YAC1C,MAAM,CAAC,KAAK,GAAG,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;YAC5C,MAAM,CAAC,KAAK,GAAG,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;YAC5C,MAAM,CAAC,OAAO,GAAG,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;YAChD,MAAM,CAAC,OAAO,GAAG,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;SACnD,MAAM;;;;;;;YAOH,IAAI,KAAK,CAAC,KAAK,KAAK,IAAI,IAAI,KAAK,CAAC,OAAO,KAAK,IAAI,EAAE;gBAChD,IAAI,QAAQ,GAAG,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,MAAM,CAAC,aAAa,IAAI,QAAQ,CAAC;gBACtE,IAAI,GAAG,GAAG,QAAQ,CAAC,eAAe,CAAC;gBACnC,IAAI,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC;;gBAEzB,MAAM,CAAC,KAAK,GAAG,KAAK,CAAC,OAAO,IAAI,GAAG,IAAI,GAAG,CAAC,UAAU,IAAI,IAAI,IAAI,IAAI,CAAC,UAAU,IAAI,CAAC,CAAC,IAAI,GAAG,IAAI,GAAG,CAAC,UAAU,IAAI,IAAI,IAAI,IAAI,CAAC,UAAU,IAAI,CAAC,CAAC,CAAC;gBACjJ,MAAM,CAAC,KAAK,GAAG,KAAK,CAAC,OAAO,IAAI,GAAG,IAAI,GAAG,CAAC,SAAS,IAAI,IAAI,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,CAAC,IAAI,GAAG,IAAI,GAAG,CAAC,SAAS,IAAI,IAAI,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,CAAC,CAAC;aAChJ,MAAM;gBACH,MAAM,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;gBAC3B,MAAM,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;aAC9B;;;;;;;YAOD,MAAM,CAAC,CAAC,GAAG,KAAK,CAAC,OAAO,CAAC;YACzB,MAAM,CAAC,CAAC,GAAG,KAAK,CAAC,OAAO,CAAC;;YAEzB,MAAM,CAAC,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;YAC/B,MAAM,CAAC,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;SAClC;;QAED,MAAM,CAAC,OAAO,GAAG,MAAM,CAAC,CAAC,CAAC;QAC1B,MAAM,CAAC,OAAO,GAAG,MAAM,CAAC,CAAC,CAAC;KAC7B,CAAC;;;CAGL,AAO4B;;ACtF7B,SAAS,gBAAgB,GAAG;IACxB,IAAI,KAAK,GAAG;QACR,GAAG,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,UAAU,EAAE,IAAI,EAAE;QACnC,IAAI,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,UAAU,EAAE,IAAI,EAAE;QACpC,KAAK,EAAE,EAAE,KAAK,EAAE,MAAM,CAAC,UAAU,EAAE,UAAU,EAAE,IAAI,EAAE;QACrD,MAAM,EAAE,EAAE,KAAK,EAAE,MAAM,CAAC,WAAW,EAAE,UAAU,EAAE,IAAI,EAAE;QACvD,KAAK,EAAE,EAAE,KAAK,EAAE,MAAM,CAAC,UAAU,EAAE,UAAU,EAAE,IAAI,EAAE;QACrD,MAAM,EAAE,EAAE,KAAK,EAAE,MAAM,CAAC,WAAW,EAAE,UAAU,EAAE,IAAI,EAAE;QACvD,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,UAAU,EAAE,IAAI,EAAE;QACjC,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,UAAU,EAAE,IAAI,EAAE;KACpC,CAAC;;IAEF,IAAI,MAAM,CAAC,MAAM,EAAE;QACf,OAAO,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;KACnC,MAAM;QACH,IAAI,IAAI,GAAG,EAAE,CAAC;QACd,MAAM,CAAC,gBAAgB,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QACrC,OAAO,IAAI,CAAC;KACf;CACJ;;AAED,SAAS,aAAa,CAAC,EAAE,EAAE;IACvB,IAAI,EAAE,KAAK,MAAM,EAAE;QACf,OAAO,gBAAgB,EAAE,CAAC;KAC7B,MAAM;QACH,IAAI;YACA,IAAI,IAAI,GAAG,EAAE,CAAC,qBAAqB,EAAE,CAAC;YACtC,IAAI,IAAI,CAAC,CAAC,KAAK,SAAS,EAAE;gBACtB,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC;gBACnB,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC;aACrB;YACD,OAAO,IAAI,CAAC;SACf,CAAC,OAAO,CAAC,EAAE;YACR,MAAM,IAAI,SAAS,CAAC,sCAAsC,GAAG,EAAE,CAAC,CAAC;SACpE;KACJ;CACJ;;AAED,SAAS,WAAW,CAAC,KAAK,EAAE,EAAE,EAAE;IAC5B,IAAI,IAAI,GAAG,aAAa,CAAC,EAAE,CAAC,CAAC;IAC7B,OAAO,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,IAAI,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,IAAI,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC;CACrG,AAED,AAAsE;;AC7CtE,IAAI,YAAY,GAAG,KAAK,CAAC,CAAC;AAC1B,IAAI,OAAO,MAAM,CAAC,MAAM,IAAI,UAAU,EAAE;EACtC,YAAY,GAAG,UAAU,SAAS,EAAE;IAClC,IAAI,IAAI,GAAG,SAAS,IAAI,GAAG,EAAE,CAAC;IAC9B,OAAO,UAAU,SAAS,EAAE,gBAAgB,EAAE;MAC5C,IAAI,SAAS,KAAK,MAAM,CAAC,SAAS,CAAC,IAAI,SAAS,KAAK,IAAI,EAAE;QACzD,MAAM,SAAS,CAAC,qCAAqC,CAAC,CAAC;OACxD;MACD,IAAI,CAAC,SAAS,GAAG,SAAS,IAAI,EAAE,CAAC;MACjC,IAAI,MAAM,GAAG,IAAI,IAAI,EAAE,CAAC;MACxB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;MACtB,IAAI,gBAAgB,KAAK,SAAS,EAAE;QAClC,MAAM,CAAC,gBAAgB,CAAC,MAAM,EAAE,gBAAgB,CAAC,CAAC;OACnD;;;MAGD,IAAI,SAAS,KAAK,IAAI,EAAE;QACtB,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC;OACzB;MACD,OAAO,MAAM,CAAC;KACf,CAAC;GACH,EAAE,CAAC;CACL,MAAM;EACL,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC;CAC9B;;AAED,IAAI,cAAc,GAAG,YAAY,CAAC;;AAElC,IAAI,eAAe,GAAG,CAAC,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,WAAW,EAAE,WAAW,EAAE,SAAS,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,eAAe,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;;AAEpP,SAAS,gBAAgB,CAAC,OAAO,EAAE;;IAE/B,IAAI,eAAe,GAAG;QAClB,OAAO,EAAE,CAAC;QACV,OAAO,EAAE,CAAC;QACV,OAAO,EAAE,CAAC;QACV,OAAO,EAAE,CAAC;QACV,OAAO,EAAE,KAAK;QACd,QAAQ,EAAE,KAAK;QACf,MAAM,EAAE,KAAK;QACb,OAAO,EAAE,KAAK;QACd,MAAM,EAAE,CAAC;QACT,OAAO,EAAE,CAAC;QACV,aAAa,EAAE,IAAI;QACnB,MAAM,EAAE,IAAI;KACf,CAAC;;IAEF,IAAI,OAAO,KAAK,SAAS,EAAE;QACvB,OAAO,CAAC,gBAAgB,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;KACjD;;IAED,SAAS,MAAM,CAAC,CAAC,EAAE;QACf,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,eAAe,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC7C,eAAe,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC;SAC/D;KACJ;;IAED,IAAI,QAAQ,GAAG,YAAY;QACvB,IAAI,UAAU,EAAE;YACZ,OAAO,SAAS,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE;gBACxC,IAAI,GAAG,GAAG,IAAI,UAAU,CAAC,WAAW,EAAE,cAAc,CAAC,eAAe,EAAE,QAAQ,CAAC,CAAC,CAAC;;;gBAGjF,UAAU,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;;gBAEtB,OAAO,OAAO,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;aACrC,CAAC;SACL,MAAM,IAAI,OAAO,QAAQ,CAAC,WAAW,KAAK,UAAU,EAAE;YACnD,OAAO,SAAS,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE;gBACxC,IAAI,QAAQ,GAAG,cAAc,CAAC,eAAe,EAAE,QAAQ,CAAC,CAAC;gBACzD,IAAI,GAAG,GAAG,QAAQ,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC;;gBAE9C,GAAG,CAAC,cAAc,CAAC,WAAW,EAAE,IAAI;gBACpC,IAAI;gBACJ,MAAM;gBACN,CAAC;gBACD,QAAQ,CAAC,OAAO;gBAChB,QAAQ,CAAC,OAAO;gBAChB,QAAQ,CAAC,OAAO;gBAChB,QAAQ,CAAC,OAAO;gBAChB,QAAQ,CAAC,OAAO;gBAChB,QAAQ,CAAC,MAAM;gBACf,QAAQ,CAAC,QAAQ;gBACjB,QAAQ,CAAC,OAAO;gBAChB,QAAQ,CAAC,MAAM;gBACf,QAAQ,CAAC,aAAa;iBACrB,CAAC;;;gBAGF,UAAU,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;;gBAEtB,OAAO,OAAO,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;aACrC,CAAC;SACL,MAAM,IAAI,OAAO,QAAQ,CAAC,iBAAiB,KAAK,UAAU,EAAE;YACzD,OAAO,SAAS,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE;gBACxC,IAAI,GAAG,GAAG,QAAQ,CAAC,iBAAiB,EAAE,CAAC;gBACvC,IAAI,QAAQ,GAAG,cAAc,CAAC,eAAe,EAAE,QAAQ,CAAC,CAAC;gBACzD,KAAK,IAAI,IAAI,IAAI,QAAQ,EAAE;oBACvB,GAAG,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC;iBAC9B;;;gBAGD,UAAU,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;;gBAEtB,OAAO,OAAO,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;aACrC,CAAC;SACL;KACJ,EAAE,CAAC;;IAEJ,SAAS,OAAO,GAAG;QACf,IAAI,OAAO,EAAE,EAAA,OAAO,CAAC,mBAAmB,CAAC,WAAW,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC,EAAA;QACrE,eAAe,GAAG,IAAI,CAAC;KAC1B;;IAED,OAAO;QACH,OAAO,EAAE,OAAO;QAChB,QAAQ,EAAE,QAAQ;KACrB,CAAC;CACL;;AAED,SAAS,cAAc,CAAC,eAAe,EAAE,QAAQ,EAAE;IAC/C,QAAQ,GAAG,QAAQ,IAAI,EAAE,CAAC;IAC1B,IAAI,QAAQ,GAAG,cAAc,CAAC,eAAe,CAAC,CAAC;IAC/C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,eAAe,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QAC7C,IAAI,QAAQ,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,KAAK,SAAS,EAAE,EAAA,QAAQ,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,EAAA;KAC/G;;IAED,OAAO,QAAQ,CAAC;CACnB;;AAED,SAAS,UAAU,CAAC,CAAC,EAAE,IAAI,EAAE;IACzB,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;IAC3B,CAAC,CAAC,IAAI,GAAG,IAAI,IAAI,EAAE,CAAC;IACpB,CAAC,CAAC,UAAU,GAAG,WAAW,CAAC;CAC9B,AAM+B;;ACzHhC,SAAS,YAAY,CAAC,QAAQ,EAAE,OAAY,CAAC;qCAAN,GAAG,EAAE;;IACxCC,IAAM,IAAI,GAAG,IAAI,CAAC;IAClBC,IAAI,QAAQ,GAAG,CAAC,EAAE,SAAS,GAAG,KAAK,CAAC;;IAEpC,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,IAAI,CAAC,CAAC,CAAC;;IAEnC,IAAI,CAAC,iBAAiB,GAAG,OAAO,CAAC,iBAAiB,IAAI,KAAK,CAAC;;IAE5DA,IAAI,KAAK,GAAG,EAAE;QACV,OAAO,GAAG,aAAa,CAAC,KAAK,CAAC;QAC9B,UAAU,GAAGC,gBAAmB,EAAE;QAClC,IAAI,GAAG,KAAK,CAAC;;IAEjB,MAAM,CAAC,gBAAgB,CAAC,WAAW,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;IACrD,MAAM,CAAC,gBAAgB,CAAC,WAAW,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;;IAErD,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QACxB,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;KAC/B;;IAED,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;IAC9C,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,OAAO,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;;IAEjD,IAAI,CAAC,OAAO,GAAG,SAAS,mBAAmB,EAAE;QACzC,MAAM,CAAC,mBAAmB,CAAC,WAAW,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;QACxD,MAAM,CAAC,mBAAmB,CAAC,WAAW,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;QACxD,MAAM,CAAC,mBAAmB,CAAC,WAAW,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;QACvD,MAAM,CAAC,mBAAmB,CAAC,YAAY,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;QACxD,MAAM,CAAC,mBAAmB,CAAC,SAAS,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;QACnD,MAAM,CAAC,mBAAmB,CAAC,UAAU,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;QACpD,MAAM,CAAC,mBAAmB,CAAC,WAAW,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;QACrD,MAAM,CAAC,mBAAmB,CAAC,YAAY,EAAE,UAAU,EAAE,KAAK,CAAC,CAAC;;QAE5D,MAAM,CAAC,mBAAmB,CAAC,WAAW,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;QACvD,MAAM,CAAC,mBAAmB,CAAC,WAAW,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;;QAEvD,MAAM,CAAC,mBAAmB,CAAC,QAAQ,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;QACtD,QAAQ,GAAG,EAAE,CAAC;QACd,GAAG,mBAAmB,CAAC;UACrB,cAAc,EAAE,CAAC;SAClB;KACJ,CAAC;;IAEF,IAAI,CAAC,GAAG,GAAG,UAAoB;;;;QAC3B,WAAW,MAAA,CAAC,UAAA,QAAQ,WAAE,OAAU,EAAA,CAAC,CAAC;QAClC,OAAO,IAAI,CAAC;KACf,CAAC;;IAEF,IAAI,CAAC,MAAM,GAAG,UAAoB;;;;QAC9B,OAAO,cAAc,MAAA,CAAC,UAAA,QAAQ,WAAE,OAAU,EAAA,CAAC,CAAC;KAC/C,CAAC;;IAEFD,IAAI,SAAS,GAAG,IAAI,EAAE,oBAAoB,CAAC;;IAE3C,GAAG,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,gBAAgB,CAAC;QAC7D,QAAQ,GAAG,CAAC,QAAQ,CAAC,CAAC;KACzB;;IAED,CAAC,SAAS,IAAI,CAAC;QACX,QAAQ,GAAG,EAAE,CAAC;QACd,IAAI,CAAC,OAAO,CAAC,SAAS,OAAO,CAAC;YAC1B,GAAG,OAAO,KAAK,MAAM,CAAC;gBAClB,SAAS,GAAG,MAAM,CAAC;aACtB,IAAI;gBACD,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;aACrB;SACJ,CAAC,CAAA;KACL,CAAC,QAAQ,CAAC,EAAE;;IAEb,MAAM,CAAC,gBAAgB,CAAC,IAAI,EAAE;QAC1B,IAAI,EAAE;YACF,GAAG,EAAE,UAAU,EAAE,OAAO,IAAI,CAAC,EAAE;SAClC;QACD,QAAQ,EAAE;YACN,GAAG,EAAE,UAAU,EAAE,OAAO,QAAQ,CAAC,EAAE;SACtC;QACD,KAAK,EAAE;YACH,GAAG,EAAE,UAAU,EAAE,OAAO,KAAK,CAAC,EAAE;SACnC;QACD,SAAS,EAAE;YACP,GAAG,EAAE,UAAU,EAAE,OAAO,SAAS,CAAC,EAAE;SACvC;KACJ,CAAC,CAAC;;IAEHA,IAAI,CAAC,GAAG,CAAC,EAAE,OAAO,GAAG,IAAI,EAAE,cAAc,CAAC;;IAE1C,MAAM,CAAC,gBAAgB,CAAC,WAAW,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;IACpD,MAAM,CAAC,gBAAgB,CAAC,YAAY,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;IACrD,MAAM,CAAC,gBAAgB,CAAC,SAAS,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;IAChD,MAAM,CAAC,gBAAgB,CAAC,UAAU,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;;;;;;;;IAQjD,MAAM,CAAC,gBAAgB,CAAC,WAAW,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;;IAElD,MAAM,CAAC,gBAAgB,CAAC,WAAW,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;IACpD,MAAM,CAAC,gBAAgB,CAAC,WAAW,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;;IAEpD,MAAM,CAAC,gBAAgB,CAAC,YAAY,EAAE,UAAU,EAAE,KAAK,CAAC,CAAC;;IAEzD,MAAM,CAAC,gBAAgB,CAAC,QAAQ,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;;IAEnD,SAAS,SAAS,CAAC,CAAC,CAAC;;QAEjB,IAAIA,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC;YAChC,GAAG,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;gBACxB,SAAS,GAAG,IAAI,CAAC;gBACjB,MAAM;aACT;SACJ;;QAED,GAAG,SAAS,CAAC;YACTE,qBAAY,CAAC,WAAE,SAAE,SAAS,GAAG,KAAK,GAAA,CAAC,CAAA;SACtC;KACJ;;IAED,SAAS,MAAM,EAAE;QACb,IAAI,GAAG,IAAI,CAAC;KACf;;IAED,SAAS,IAAI,EAAE;QACX,IAAI,GAAG,KAAK,CAAC;QACb,cAAc,EAAE,CAAC;KACpB;IACD,SAAS,cAAc,EAAE;MACvBC,oBAAW,CAAC,cAAc,CAAC,CAAC;MAC5BA,oBAAW,CAAC,oBAAoB,CAAC,CAAC;KACnC;IACD,SAAS,UAAU,EAAE;QACjB,IAAI,GAAG,KAAK,CAAC;KAChB;;IAED,SAAS,SAAS,CAAC,MAAM,CAAC;QACtB,GAAG,CAAC,MAAM,CAAC;YACP,OAAO,IAAI,CAAC;SACf;;QAED,GAAG,OAAO,KAAK,MAAM,CAAC;YAClB,OAAO,MAAM,CAAC;SACjB;;QAED,GAAG,UAAU,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;YAC5B,OAAO,MAAM,CAAC;SACjB;;QAED,MAAM,MAAM,GAAG,MAAM,CAAC,UAAU,CAAC;YAC7B,GAAG,UAAU,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;gBAC5B,OAAO,MAAM,CAAC;aACjB;SACJ;;QAED,OAAO,IAAI,CAAC;KACf;;IAED,SAAS,oBAAoB,EAAE;QAC3BH,IAAI,UAAU,GAAG,IAAI,CAAC;;QAEtB,IAAI,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC;YAChC,GAAG,MAAM,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC1B,UAAU,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;aAC5B;SACJ;;QAED,OAAO,UAAU,CAAC;KACrB;;;IAGD,SAAS,MAAM,CAAC,KAAK,CAAC;;QAElB,GAAG,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,EAAA,OAAO,EAAA;;QAE9B,GAAG,KAAK,CAAC,YAAY,CAAC,CAAC,EAAE,OAAO,EAAE;;QAElCA,IAAI,MAAM,GAAG,KAAK,CAAC,MAAM,EAAE,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC;;QAEhD,GAAG,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;YAClC,GAAG,CAAC,IAAI,CAAC,iBAAiB,CAAC;gBACvB,OAAO,GAAG,IAAI,CAAC;aAClB;SACJ;;QAED,GAAG,MAAM,IAAI,MAAM,CAAC,UAAU,KAAK,IAAI,CAAC;;YAEpC,MAAM,GAAG,oBAAoB,EAAE,CAAC;SACnC,IAAI;YACD,MAAM,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC;;YAE3B,GAAG,CAAC,MAAM,CAAC;gBACP,MAAM,GAAG,oBAAoB,EAAE,CAAC;aACnC;SACJ;;;QAGD,GAAG,MAAM,IAAI,MAAM,KAAK,OAAO,CAAC;YAC5B,OAAO,GAAG,MAAM,CAAC;SACpB;;QAED,GAAG,SAAS,CAAC;YACTG,oBAAW,CAAC,oBAAoB,CAAC,CAAC;YAClC,oBAAoB,GAAGD,qBAAY,CAAC,YAAY,CAAC,CAAC;SACrD;;;QAGD,GAAG,CAAC,OAAO,CAAC;YACR,OAAO;SACV;;QAEDC,oBAAW,CAAC,cAAc,CAAC,CAAC;QAC5B,cAAc,GAAGD,qBAAY,CAAC,UAAU,CAAC,CAAC;KAC7C;;IAED,SAAS,YAAY,EAAE;QACnB,UAAU,CAAC,SAAS,CAAC,CAAC;;QAEtBC,oBAAW,CAAC,oBAAoB,CAAC,CAAC;QAClC,oBAAoB,GAAGD,qBAAY,CAAC,YAAY,CAAC,CAAC;KACrD;;IAED,SAAS,UAAU,EAAE;;QAEjB,GAAG,CAAC,OAAO,CAAC;YACR,OAAO;SACV;;QAED,UAAU,CAAC,OAAO,CAAC,CAAC;;QAEpBC,oBAAW,CAAC,cAAc,CAAC,CAAC;QAC5B,cAAc,GAAGD,qBAAY,CAAC,UAAU,CAAC,CAAC;;KAE7C;;;IAGD,SAAS,UAAU,CAAC,EAAE,CAAC;QACnBF,IAAI,IAAI,GAAGI,aAAO,CAAC,EAAE,CAAC,EAAE,OAAO,EAAE,OAAO,CAAC;;QAEzC,GAAG,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC;YACjC,OAAO,GAAG,IAAI,CAAC,KAAK;gBAChB,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ;aACxE,CAAC;SACL,KAAK,GAAG,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;YACxC,OAAO,GAAG,IAAI,CAAC,IAAI;gBACf,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ;aACxE,CAAC;SACL,IAAI;YACD,OAAO,GAAG,CAAC,CAAC;SACf;;QAED,GAAG,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC;YAChC,OAAO,GAAG,IAAI,CAAC,KAAK;gBAChB,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ;aACvE,CAAC;SACL,KAAK,GAAG,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;YACzC,OAAO,GAAG,IAAI,CAAC,IAAI;gBACf,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ;aACzE,CAAC;SACL,IAAI;YACD,OAAO,GAAG,CAAC,CAAC;SACf;;QAED,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;;;;;;;YAOf,UAAU,CAAC,QAAQ,CAAC,EAAE,EAAE;gBACpB,KAAK,EAAE,KAAK,CAAC,KAAK,GAAG,OAAO;gBAC5B,KAAK,EAAE,KAAK,CAAC,KAAK,GAAG,OAAO;gBAC5B,OAAO,EAAE,KAAK,CAAC,CAAC,GAAG,OAAO;gBAC1B,OAAO,EAAE,KAAK,CAAC,CAAC,GAAG,OAAO;aAC7B,CAAC,CAAC;SACN;;QAED,UAAU,CAAC,WAAE;;YAET,GAAG,OAAO,CAAC;gBACP,OAAO,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;aACxB;;YAED,GAAG,OAAO,CAAC;gBACP,OAAO,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;aACxB;;SAEJ,CAAC,CAAC;KACN;;IAED,SAAS,OAAO,CAAC,EAAE,EAAE,MAAM,CAAC;QACxB,GAAG,EAAE,KAAK,MAAM,CAAC;YACb,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,WAAW,EAAE,EAAE,CAAC,WAAW,GAAG,MAAM,CAAC,CAAC;SAC5D,IAAI;YACD,EAAE,CAAC,SAAS,IAAI,MAAM,CAAC;SAC1B;KACJ;;IAED,SAAS,OAAO,CAAC,EAAE,EAAE,MAAM,CAAC;QACxB,GAAG,EAAE,KAAK,MAAM,CAAC;YACb,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,WAAW,GAAG,MAAM,EAAE,EAAE,CAAC,WAAW,CAAC,CAAC;SAC5D,IAAI;YACD,EAAE,CAAC,UAAU,IAAI,MAAM,CAAC;SAC3B;KACJ;;CAEJ;;AAED,AAAe,SAAS,mBAAmB,CAAC,OAAO,EAAE,OAAO,CAAC;IACzD,OAAO,IAAI,YAAY,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;CAC7C;;AAED,SAAS,MAAM,CAAC,KAAK,EAAE,EAAE,EAAE,IAAI,CAAC;IAC5B,GAAG,CAAC,IAAI,CAAC;QACL,OAAO,WAAW,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;KACjC,IAAI;QACD,QAAQ,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM;gBAC3C,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,IAAI,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,EAAE;KACxD;CACJ;;;;;;;;;"}