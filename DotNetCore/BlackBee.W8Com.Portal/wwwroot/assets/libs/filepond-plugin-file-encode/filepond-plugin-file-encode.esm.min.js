/*!
 * FilePondPluginFileEncode 2.1.10
 * Licensed under MIT, https://opensource.org/licenses/MIT/
 * Please visit https://pqina.nl/filepond/ for details.
 */

/* eslint-disable */

const e=function(){self.onmessage=(a=>{e(a.data.message,e=>{self.postMessage({id:a.data.id,message:e})})});const e=(e,a)=>{const{file:t}=e,i=new FileReader;i.onloadend=(()=>{a(i.result.replace("data:","").replace(/^.+,/,""))}),i.readAsDataURL(t)}},a=({addFilter:a,utils:t})=>{const{Type:i,createWorker:d,createRoute:n,isFile:E}=t,o=({name:a,file:t})=>new Promise(i=>{const n=d(e);n.post({file:t},e=>{i({name:a,data:e}),n.terminate()})}),r=[];return a("DID_CREATE_ITEM",(e,{query:a})=>{a("GET_ALLOW_FILE_ENCODE")&&(e.extend("getFileEncodeBase64String",()=>r[e.id]&&r[e.id].data),e.extend("getFileEncodeDataURL",()=>`data:${e.fileType};base64,${r[e.id].data}`))}),a("SHOULD_PREPARE_OUTPUT",(e,{query:a})=>new Promise(e=>{e(a("GET_ALLOW_FILE_ENCODE"))})),a("COMPLETE_PREPARE_OUTPUT",(e,{item:a,query:t})=>new Promise(i=>{if(!t("GET_ALLOW_FILE_ENCODE")||!E(e)&&!Array.isArray(e))return i(e);r[a.id]={metadata:a.getMetadata(),data:null},Promise.all((e instanceof Blob?[{name:null,file:e}]:e).map(o)).then(t=>{r[a.id].data=e instanceof Blob?t[0].data:t,i(e)})})),a("CREATE_VIEW",e=>{const{is:a,view:t,query:i}=e;a("file-wrapper")&&i("GET_ALLOW_FILE_ENCODE")&&t.registerWriter(n({DID_PREPARE_OUTPUT:({root:e,action:a})=>{if(i("IS_ASYNC"))return;const t=i("GET_ITEM",a.id);if(!t)return;const d=r[t.id],n=d.metadata,E=d.data,o=JSON.stringify({id:t.id,name:t.file.name,type:t.file.type,size:t.file.size,metadata:n,data:E});e.ref.data?e.ref.data.value=o:e.dispatch("DID_DEFINE_VALUE",{id:t.id,value:o})},DID_REMOVE_ITEM:({action:e})=>{const a=i("GET_ITEM",e.id);a&&delete r[a.id]}}))}),{options:{allowFileEncode:[!0,i.BOOLEAN]}}};"undefined"!=typeof window&&void 0!==window.document&&document.dispatchEvent(new CustomEvent("FilePond:pluginloaded",{detail:a}));export default a;
