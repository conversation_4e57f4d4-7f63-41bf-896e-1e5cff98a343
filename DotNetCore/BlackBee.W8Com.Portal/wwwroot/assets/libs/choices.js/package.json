{"name": "choices.js", "version": "10.2.0", "description": "A vanilla JS customisable text input/select box plugin", "main": "./public/assets/scripts/choices.js", "types": "./public/types/src/index.d.ts", "scripts": {"start": "run-p js:watch css:watch", "build": "run-p js:build css:build", "lint": "run-p lint:js lint:scss", "lint:js": "eslint src/scripts/**/*.ts", "lint:scss": "stylelint src/**/*.scss", "bundlesize": "bundlesize", "cypress:run": "cypress run --browser chrome", "cypress:open": "cypress open", "cypress:ci": "cypress run --browser chrome --record --group $GITHUB_REF --ci-build-id $GITHUB_SHA", "test": "run-s test:unit test:e2e", "test:unit": "cross-env TS_NODE_TRANSPILE_ONLY=true NODE_ENV=test mocha", "test:unit:watch": "npm run test:unit -- --watch --inspect=5556", "test:unit:coverage": "NODE_ENV=test nyc --reporter=lcov --reporter=text --reporter=text-summary mocha", "test:e2e": "run-p --race start cypress:run", "js:watch": "cross-env NODE_ENV=development node server.js", "js:build": "webpack --config webpack.config.prod.js", "css:watch": "nodemon -e scss -x \"npm run css:build\"", "css:build": "run-s css:sass css:prefix css:min", "css:sass": "sass -I scss src/styles/base.scss public/assets/styles/base.css && sass -I scss src/styles/choices.scss public/assets/styles/choices.css", "css:prefix": "postcss public/assets/styles/*.css --use autoprefixer --no-map --env prod --dir public/assets/styles", "css:min": "csso public/assets/styles/base.css --output public/assets/styles/base.min.css && csso public/assets/styles/choices.css --output public/assets/styles/choices.min.css", "deploy": "git subtree push --prefix public origin gh-pages", "prepublishOnly": "npm run build"}, "repository": {"type": "git", "url": "git+https://github.com/jshjohnson/Choices.git"}, "author": "<PERSON>", "license": "MIT", "files": ["public/assets/scripts", "public/assets/styles", "public/types", "src", "!src/**/*.test.js", "types"], "bugs": {"url": "https://github.com/jshjo<PERSON>son/Choices/issues"}, "homepage": "https://github.com/jshjo<PERSON>son/Choices#readme", "keywords": ["customisable", "input", "select", "vanilla", "plugin", "js"], "devDependencies": {"@babel/core": "^7.20.5", "@babel/preset-env": "^7.20.2", "@babel/register": "^7.18.9", "@types/chai": "^4.3.4", "@types/mocha": "^10.0.1", "@types/sinon": "^10.0.13", "@types/sinon-chai": "^3.2.9", "@typescript-eslint/eslint-plugin": "^5.45.0", "@typescript-eslint/parser": "^5.45.0", "autoprefixer": "^10.4.13", "babel-loader": "^9.1.0", "bundlesize": "^0.18.1", "chai": "^4.3.7", "cross-env": "^7.0.3", "csso-cli": "^4.0.1", "cypress": "11.2.0", "eslint": "^8.28.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-airbnb-typescript": "^17.0.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-compat": "4.0.2", "eslint-plugin-cypress": "^2.12.1", "eslint-plugin-import": "^2.26.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-sort-class-members": "^1.15.2", "eslint-webpack-plugin": "^3.2.0", "express": "^4.18.2", "husky": "^8.0.2", "jsdom": "^20.0.3", "lint-staged": "^13.0.4", "mocha": "^10.1.0", "nodemon": "^2.0.20", "npm-run-all": "^4.1.5", "nyc": "^15.1.0", "postcss": "^8.4.19", "postcss-cli": "^10.0.0", "prettier": "^2.8.0", "sass": "^1.56.1", "sinon": "^15.0.0", "sinon-chai": "^3.7.0", "stylelint": "^14.15.0", "stylelint-config-standard": "^29.0.0", "stylelint-config-standard-scss": "^6.1.0", "ts-loader": "^9.4.1", "ts-node": "^10.9.1", "typescript": "^4.9.3", "webpack": "^5.75.0", "webpack-cli": "^5.0.0", "webpack-dev-middleware": "^6.0.1", "webpack-hot-middleware": "^2.25.3"}, "dependencies": {"deepmerge": "^4.2.2", "fuse.js": "^6.6.2", "redux": "^4.2.0"}, "npmName": "choices.js", "npmFileMap": [{"files": ["public/assets/scripts/*", "public/assets/styles/*", "public/types/*", "src/icons/*"]}], "nyc": {"include": ["src/scripts/**/**/*.js"], "exclude": ["src/scripts/**/**/*.test.js"]}, "bundlesize": [{"path": "public/assets/scripts/choices.min.js", "maxSize": "25 kB"}, {"path": "public/assets/styles/choices.min.css", "maxSize": "2.5 kB"}]}