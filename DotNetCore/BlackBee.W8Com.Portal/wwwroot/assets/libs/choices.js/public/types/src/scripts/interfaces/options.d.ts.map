{"version": 3, "file": "options.d.ts", "sourceRoot": "", "sources": ["../../../../../src/scripts/interfaces/options.ts"], "names": [], "mappings": "AAAA,OAAO,IAAI,MAAM,SAAS,CAAC;AAC3B,OAAO,EAAE,OAAO,EAAE,MAAM,WAAW,CAAC;AACpC,OAAO,EAAE,MAAM,EAAE,MAAM,UAAU,CAAC;AAClC,OAAO,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AAC3C,OAAO,EAAE,mBAAmB,EAAE,MAAM,yBAAyB,CAAC;AAC9D,OAAO,EAAE,KAAK,EAAE,MAAM,SAAS,CAAC;AAEhC;;;;;;;;GAQG;AACH,MAAM,WAAW,OAAO;IACtB;;;;;;OAMG;IACH,MAAM,EAAE,OAAO,CAAC;IAEhB;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA4BG;IACH,KAAK,EAAE,MAAM,EAAE,GAAG,MAAM,EAAE,CAAC;IAE3B;;;;;;;;;;;;;;;;;;;;;;;;;;OA0BG;IACH,OAAO,EAAE,MAAM,EAAE,CAAC;IAElB;;;;;;OAMG;IACH,iBAAiB,EAAE,MAAM,CAAC;IAE1B;;;;;;OAMG;IACH,YAAY,EAAE,MAAM,CAAC;IAErB;;;;;;OAMG;IACH,QAAQ,EAAE,OAAO,CAAC;IAElB;;;;;;OAMG;IACH,aAAa,EAAE,MAAM,GAAG,MAAM,GAAG,KAAK,CAAC,cAAc,GAAG,IAAI,CAAC;IAE7D;;;;;;;;;OASG;IACH,WAAW,EAAE,MAAM,GAAG,KAAK,CAAC,oBAAoB,CAAC;IAEjD;;;;;;OAMG;IACH,WAAW,EAAE,OAAO,CAAC;IAErB;;;;;;OAMG;IACH,gBAAgB,EAAE,OAAO,CAAC;IAE1B;;;;;;OAMG;IACH,SAAS,EAAE,OAAO,CAAC;IAEnB;;;;;;;;;;OAUG;IACH,SAAS,EAAE,OAAO,CAAC;IAEnB;;;;;;OAMG;IACH,qBAAqB,EAAE,OAAO,CAAC;IAE/B;;;;;;OAMG;IACH,SAAS,EAAE,MAAM,CAAC;IAElB;;;;;;OAMG;IACH,KAAK,EAAE,OAAO,CAAC;IAEf;;;;;;;;OAQG;IACH,aAAa,EAAE,OAAO,CAAC;IAEvB;;;;;;OAMG;IACH,aAAa,EAAE,OAAO,CAAC;IAEvB;;;;;;OAMG;IACH,WAAW,EAAE,MAAM,CAAC;IAEpB;;;;;;OAMG;IACH,iBAAiB,EAAE,MAAM,CAAC;IAE1B;;;;;;OAMG;IACH,YAAY,EAAE,MAAM,EAAE,CAAC;IAEvB;;;;;;OAMG;IACH,QAAQ,EAAE,mBAAmB,CAAC;IAE9B;;;;;;OAMG;IACH,mBAAmB,EAAE,OAAO,CAAC;IAE7B;;;;;;OAMG;IACH,UAAU,EAAE,OAAO,CAAC;IAEpB;;;;;;OAMG;IACH,eAAe,EAAE,OAAO,CAAC;IAEzB;;;;;;;;;;;;;;;;OAgBG;IACH,MAAM,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,KAAK,MAAM,CAAC;IAElD;;;;;;;;;;;;;;;;OAgBG;IACH,WAAW,EAAE,OAAO,CAAC;IAErB;;;;;;OAMG;IACH,gBAAgB,EAAE,MAAM,GAAG,IAAI,CAAC;IAEhC;;;;;;OAMG;IACH,sBAAsB,EAAE,MAAM,GAAG,IAAI,CAAC;IAEtC;;;;;;OAMG;IACH,YAAY,EAAE,MAAM,GAAG,IAAI,CAAC;IAE5B;;;;;;OAMG;IACH,WAAW,EAAE,MAAM,GAAG,IAAI,CAAC;IAE3B;;;;;;OAMG;IACH,qBAAqB,EAAE,MAAM,GAAG,QAAQ,CAAC;IAEzC;;;;;;OAMG;IACH,WAAW,EAAE,MAAM,CAAC;IAEpB;;;;;;OAMG;IACH,aAAa,EAAE,MAAM,GAAG,KAAK,CAAC,cAAc,CAAC;IAE7C;;;;;;OAMG;IACH,aAAa,EAAE,MAAM,GAAG,KAAK,CAAC,cAAc,CAAC;IAE7C;;;;;;OAMG;IACH,cAAc,EAAE,MAAM,CAAC;IAEvB;;;;;;;;;OASG;IACH,WAAW,EAAE,MAAM,GAAG,KAAK,CAAC,mBAAmB,CAAC;IAEhD;;;;OAIG;IACH,cAAc,EAAE,MAAM,GAAG,KAAK,CAAC,oBAAoB,CAAC;IAEpD;;;;;;OAMG;IACH,iBAAiB,EAAE,MAAM,GAAG,KAAK,CAAC,oBAAoB,CAAC;IAEvD;;;;;;;;;OASG;IACH,aAAa,EAAE,KAAK,CAAC,oBAAoB,CAAC;IAE1C;;;;OAIG;IACH,UAAU,EAAE,UAAU,CAAC;IAEvB;;OAEG;IACH,WAAW,EAAE,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;IAExC;;OAEG;IACH,OAAO,EAAE,MAAM,CAAC;IAEhB;;;;;;;;OAQG;IACH,cAAc,EAAE,CAAC,CAAC,IAAI,EAAE,OAAO,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC;IAEjD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAiCG;IACH,yBAAyB,EAAE,CAAC,CAAC,QAAQ,EAAE,KAAK,CAAC,OAAO,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC;CACvE"}