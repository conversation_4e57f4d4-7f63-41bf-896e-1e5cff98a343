import { ActionType } from './interfaces/action-type';
import { EventType } from './interfaces/event-type';
import { KeyCodeMap } from './interfaces/keycode-map';
export declare const EVENTS: Record<EventType, EventType>;
export declare const ACTION_TYPES: Record<ActionType, ActionType>;
export declare const KEY_CODES: KeyCodeMap;
export declare const TEXT_TYPE: HTMLInputElement['type'];
export declare const SELECT_ONE_TYPE: HTMLSelectElement['type'];
export declare const SELECT_MULTIPLE_TYPE: HTMLSelectElement['type'];
export declare const SCROLLING_SPEED = 4;
//# sourceMappingURL=constants.d.ts.map