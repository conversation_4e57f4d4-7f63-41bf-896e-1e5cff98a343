{"version": 3, "file": "choices.d.ts", "sourceRoot": "", "sources": ["../../../../../src/scripts/interfaces/choices.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,OAAO,EAAE,MAAM,WAAW,CAAC;AACpC,OAAO,EAAE,KAAK,EAAE,MAAM,OAAO,CAAC;AAC9B,OAAO,EACL,YAAY,EACZ,aAAa,EACb,SAAS,EACT,IAAI,EACJ,KAAK,EACL,QAAQ,EACT,MAAM,eAAe,CAAC;AACvB,OAAO,EAAE,MAAM,EAAE,MAAM,UAAU,CAAC;AAClC,OAAO,EAAE,KAAK,EAAE,MAAM,SAAS,CAAC;AAChC,OAAO,EAAE,IAAI,EAAE,MAAM,QAAQ,CAAC;AAC9B,OAAO,EAAE,KAAK,EAAE,MAAM,SAAS,CAAC;AAChC,OAAO,SAAS,MAAM,cAAc,CAAC;AAErC,MAAM,WAAW,OAAO;IACtB,WAAW,EAAE,OAAO,CAAC;IACrB,MAAM,EAAE,OAAO,CAAC;IAEhB,aAAa,EAAE,YAAY,GAAG,aAAa,CAAC;IAE5C,cAAc,EAAE,SAAS,CAAC;IAE1B,cAAc,EAAE,SAAS,CAAC;IAE1B,UAAU,EAAE,IAAI,CAAC;IAEjB,QAAQ,EAAE,IAAI,CAAC;IAEf,KAAK,EAAE,KAAK,CAAC;IAEb,QAAQ,EAAE,QAAQ,CAAC;IAEnB,cAAc,EAAE,OAAO,CAAC;IAExB,mBAAmB,EAAE,OAAO,CAAC;IAE7B,wBAAwB,EAAE,OAAO,CAAC;IAElC,gBAAgB,EAAE,OAAO,CAAC;IAE1B,MAAM,EAAE,KAAK,CAAC;IAEd,UAAU,EAAE,OAAO,SAAS,CAAC;IAE7B,aAAa,EAAE,KAAK,CAAC;IAErB,aAAa,EAAE,KAAK,CAAC;IAErB,UAAU,EAAE,KAAK,CAAC;IAElB,aAAa,EAAE,MAAM,CAAC;IAEtB,UAAU,EAAE,OAAO,CAAC;IAEpB,gBAAgB,EAAE,OAAO,CAAC;IAE1B,kBAAkB,EAAE,MAAM,CAAC;IAE3B,OAAO,EAAE,OAAO,CAAC;IAEjB,YAAY,EAAE,OAAO,CAAC;IAEtB,iBAAiB,EAAE,MAAM,GAAG,IAAI,CAAC;IAEjC,OAAO,EAAE,MAAM,CAAC;IAEhB,UAAU,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC;IAE/B,QAAQ,EAAE;QACR,UAAU,EAAE,MAAM,CAAC;KACpB,CAAC;IAEF,aAAa,EAAE,KAAK,EAAE,GAAG,mBAAmB,EAAE,GAAG,OAAO,EAAE,CAAC;IAE3D,cAAc,EAAE,IAAI,EAAE,GAAG,iBAAiB,EAAE,CAAC;IAE7C,cAAc,EAAE,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;IAElC,YAAY,EAAE,IAAI,EAAE,GAAG,MAAM,EAAE,CAAC;IAEhC,KACE,OAAO,EAAE,MAAM,GAAG,OAAO,GAAG,gBAAgB,GAAG,iBAAiB,EAChE,UAAU,EAAE,OAAO,CAAC,OAAO,CAAC,OAC5B;CACH"}