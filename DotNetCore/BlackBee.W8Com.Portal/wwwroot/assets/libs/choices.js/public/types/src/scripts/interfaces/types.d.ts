export declare namespace Types {
    type StrToEl = (str: string) => HTMLElement | HTMLInputElement | HTMLOptionElement;
    type StringFunction = () => string;
    type NoticeStringFunction = (value: string) => string;
    type NoticeLimitFunction = (maxItemCount: number) => string;
    type FilterFunction = (value: string) => boolean;
    type ValueCompareFunction = (value1: string, value2: string) => boolean;
}
//# sourceMappingURL=types.d.ts.map