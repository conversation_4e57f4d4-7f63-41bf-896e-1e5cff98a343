/** Classes added to HTML generated by  By default classnames follow the BEM notation. */
export interface ClassNames {
    /** @default 'choices' */
    containerOuter: string;
    /** @default 'choices__inner' */
    containerInner: string;
    /** @default 'choices__input' */
    input: string;
    /** @default 'choices__input--cloned' */
    inputCloned: string;
    /** @default 'choices__list' */
    list: string;
    /** @default 'choices__list--multiple' */
    listItems: string;
    /** @default 'choices__list--single' */
    listSingle: string;
    /** @default 'choices__list--dropdown' */
    listDropdown: string;
    /** @default 'choices__item' */
    item: string;
    /** @default 'choices__item--selectable' */
    itemSelectable: string;
    /** @default 'choices__item--disabled' */
    itemDisabled: string;
    /** @default 'choices__item--choice' */
    itemChoice: string;
    /** @default 'choices__placeholder' */
    placeholder: string;
    /** @default 'choices__group' */
    group: string;
    /** @default 'choices__heading' */
    groupHeading: string;
    /** @default 'choices__button' */
    button: string;
    /** @default 'is-active' */
    activeState: string;
    /** @default 'is-focused' */
    focusState: string;
    /** @default 'is-open' */
    openState: string;
    /** @default 'is-disabled' */
    disabledState: string;
    /** @default 'is-highlighted' */
    highlightedState: string;
    /** @default 'is-selected' */
    selectedState: string;
    /** @default 'is-flipped' */
    flippedState: string;
    /** @default 'is-loading' */
    loadingState: string;
    /** @default 'has-no-results' */
    noResults: string;
    /** @default 'has-no-choices' */
    noChoices: string;
}
//# sourceMappingURL=class-names.d.ts.map