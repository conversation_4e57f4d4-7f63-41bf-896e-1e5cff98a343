{"version": 3, "file": "passed-element.d.ts", "sourceRoot": "", "sources": ["../../../../../src/scripts/interfaces/passed-element.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,OAAO,EAAE,MAAM,WAAW,CAAC;AACpC,OAAO,EAAE,MAAM,EAAE,MAAM,UAAU,CAAC;AAClC,OAAO,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AAC3C,OAAO,EAAE,SAAS,EAAE,MAAM,cAAc,CAAC;AACzC,OAAO,EAAE,iBAAiB,EAAE,MAAM,uBAAuB,CAAC;AAE1D,MAAM,WAAW,aAAc,SAAQ,WAAW;IAChD,UAAU,EAAE,UAAU,CAAC;IACvB,OAAO,EAAE,CAAC,gBAAgB,GAAG,iBAAiB,CAAC,GAAG;QAEhD,gBAAgB,CAAC,CAAC,SAAS,SAAS,EAClC,IAAI,EAAE,CAAC,EACP,QAAQ,EAAE,CACR,IAAI,EAAE,gBAAgB,GAAG,iBAAiB,EAC1C,EAAE,EAAE,QAAQ,CAAC,CAAC,CAAC,KACZ,IAAI,EACT,OAAO,CAAC,EAAE,OAAO,GAAG,uBAAuB,GAC1C,IAAI,CAAC;KACT,CAAC;IACF,IAAI,EAAE,iBAAiB,CAAC;IACxB,UAAU,EAAE,OAAO,CAAC;IACpB,cAAc,EAAE,OAAO,CAAC;CACzB;AAED;;GAEG;AACH,MAAM,WAAW,QAAQ;IACvB;;;;;;OAMG;IACH,OAAO,EAAE,WAAW,CAAC;QACnB,EAAE,EAAE,MAAM,CAAC;QACX,KAAK,EAAE,MAAM,CAAC;QACd,KAAK,EAAE,MAAM,CAAC;QACd,UAAU,EAAE,MAAM,CAAC;QACnB,OAAO,EAAE,MAAM,CAAC;KACjB,CAAC,CAAC;IAEH;;;;;;OAMG;IACH,UAAU,EAAE,WAAW,CAAC;QACtB,EAAE,EAAE,MAAM,CAAC;QACX,KAAK,EAAE,MAAM,CAAC;QACd,KAAK,EAAE,MAAM,CAAC;QACd,UAAU,EAAE,MAAM,CAAC;KACpB,CAAC,CAAC;IAEH;;;;;;OAMG;IACH,aAAa,EAAE,WAAW,CAAC;QACzB,EAAE,EAAE,MAAM,CAAC;QACX,KAAK,EAAE,MAAM,CAAC;QACd,KAAK,EAAE,MAAM,CAAC;QACd,UAAU,EAAE,MAAM,CAAC;KACpB,CAAC,CAAC;IAEH;;;;;;OAMG;IACH,eAAe,EAAE,WAAW,CAAC;QAC3B,EAAE,EAAE,MAAM,CAAC;QACX,KAAK,EAAE,MAAM,CAAC;QACd,KAAK,EAAE,MAAM,CAAC;QACd,UAAU,EAAE,MAAM,CAAC;KACpB,CAAC,CAAC;IAEH;;;;;;OAMG;IACH,MAAM,EAAE,WAAW,CAAC;QAAE,MAAM,EAAE,MAAM,CAAA;KAAE,CAAC,CAAC;IAExC;;;;;;OAMG;IACH,MAAM,EAAE,WAAW,CAAC;QAAE,KAAK,EAAE,MAAM,CAAA;KAAE,CAAC,CAAC;IAEvC;;;;;;OAMG;IACH,MAAM,EAAE,WAAW,CAAC;QAAE,KAAK,EAAE,MAAM,CAAC;QAAC,WAAW,EAAE,MAAM,CAAA;KAAE,CAAC,CAAC;IAE5D;;;;;;OAMG;IACH,YAAY,EAAE,WAAW,CAAC,SAAS,CAAC,CAAC;IAErC;;;;;;OAMG;IACH,YAAY,EAAE,WAAW,CAAC,SAAS,CAAC,CAAC;IAErC;;;;;OAKG;IACH,eAAe,EAAE,WAAW,CAAC;QAAE,EAAE,EAAE,aAAa,CAAA;KAAE,CAAC,CAAC;CACrD"}