/*! shepherd.js 11.1.1 */

function aa(a){var b;if(b=!!a&&"object"===typeof a)b=Object.prototype.toString.call(a),b=!("[object RegExp]"===b||"[object Date]"===b||a.$$typeof===ba);return b}var ba="function"===typeof Symbol&&Symbol.for?Symbol.for("react.element"):60103;function ca(a,b){return!1!==b.clone&&b.isMergeableObject(a)?da(Array.isArray(a)?[]:{},a,b):a}function ea(a,b,c){return a.concat(b).map(function(d){return ca(d,c)})}
function fa(a){return Object.getOwnPropertySymbols?Object.getOwnPropertySymbols(a).filter(function(b){return Object.propertyIsEnumerable.call(a,b)}):[]}function ha(a){return Object.keys(a).concat(fa(a))}function ia(a,b){try{return b in a}catch(c){return!1}}
function ja(a,b,c){var d={};c.isMergeableObject(a)&&ha(a).forEach(function(e){d[e]=ca(a[e],c)});ha(b).forEach(function(e){if(!ia(a,e)||Object.hasOwnProperty.call(a,e)&&Object.propertyIsEnumerable.call(a,e))if(ia(a,e)&&c.isMergeableObject(b[e])){if(c.customMerge){var f=c.customMerge(e);f="function"===typeof f?f:da}else f=da;d[e]=f(a[e],b[e],c)}else d[e]=ca(b[e],c)});return d}
function da(a,b,c){c=c||{};c.arrayMerge=c.arrayMerge||ea;c.isMergeableObject=c.isMergeableObject||aa;c.cloneUnlessOtherwiseSpecified=ca;var d=Array.isArray(b),e=Array.isArray(a);return d!==e?ca(b,c):d?c.arrayMerge(a,b,c):ja(a,b,c)}da.all=function(a,b){if(!Array.isArray(a))throw Error("first argument should be an array");return a.reduce(function(c,d){return da(c,d,b)},{})};var ka=da;function z(a){return"function"===typeof a}function la(a){return"string"===typeof a}
class ma{on(a,b,c,d){void 0===d&&(d=!1);void 0===this.bindings&&(this.bindings={});void 0===this.bindings[a]&&(this.bindings[a]=[]);this.bindings[a].push({handler:b,ctx:c,once:d});return this}once(a,b,c){return this.on(a,b,c,!0)}off(a,b){if(void 0===this.bindings||void 0===this.bindings[a])return this;void 0===b?delete this.bindings[a]:this.bindings[a].forEach((c,d)=>{c.handler===b&&this.bindings[a].splice(d,1)});return this}trigger(a){for(var b=arguments.length,c=Array(1<b?b-1:0),d=1;d<b;d++)c[d-
1]=arguments[d];void 0!==this.bindings&&this.bindings[a]&&this.bindings[a].forEach((e,f)=>{let {ctx:g,handler:h,once:k}=e;h.apply(g||this,c);k&&this.bindings[a].splice(f,1)});return this}}function na(a){let b=Object.getOwnPropertyNames(a.constructor.prototype);for(let c=0;c<b.length;c++){let d=b[c],e=a[d];"constructor"!==d&&"function"===typeof e&&(a[d]=e.bind(a))}return a}
function oa(a,b){return c=>{if(b.isOpen()){let d=b.el&&c.currentTarget===b.el;(void 0!==a&&c.currentTarget.matches(a)||d)&&b.tour.next()}}}
function pa(a){let {event:b,selector:c}=a.options.advanceOn||{};if(b){let d=oa(c,a),e;try{e=document.querySelector(c)}catch(f){}if(void 0===c||e)e?(e.addEventListener(b,d),a.on("destroy",()=>e.removeEventListener(b,d))):(document.body.addEventListener(b,d,!0),a.on("destroy",()=>document.body.removeEventListener(b,d,!0)));else return console.error(`No element was found for the selector supplied to advanceOn: ${c}`)}else return console.error("advanceOn was defined, but no event name was passed.")}
function qa(a){return la(a)&&""!==a?"-"!==a.charAt(a.length-1)?`${a}-`:a:""}function ra(){let a=Date.now();return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,b=>{let c=(a+16*Math.random())%16|0;a=Math.floor(a/16);return("x"==b?c:c&3|8).toString(16)})}function A(){A=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b],d;for(d in c)Object.prototype.hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a};return A.apply(this,arguments)}
function sa(a,b){if(null==a)return{};var c={},d=Object.keys(a),e;for(e=0;e<d.length;e++){var f=d[e];0<=b.indexOf(f)||(c[f]=a[f])}return c}let ta="mainAxis crossAxis fallbackPlacements fallbackStrategy fallbackAxisSideDirection flipAlignment".split(" "),ua=["mainAxis","crossAxis","limiter"];function va(a){return"y"===a?"height":"width"}function C(a){return a.split("-")[0]}function wa(a){return["top","bottom"].includes(C(a))?"x":"y"}
function xa(a,b,c){let {reference:d,floating:e}=a;var f=d.x+d.width/2-e.width/2;let g=d.y+d.height/2-e.height/2;a=wa(b);var h=va(a);h=d[h]/2-e[h]/2;let k=C(b),l="x"===a;switch(k){case "top":f={x:f,y:d.y-e.height};break;case "bottom":f={x:f,y:d.y+d.height};break;case "right":f={x:d.x+d.width,y:g};break;case "left":f={x:d.x-e.width,y:g};break;default:f={x:d.x,y:d.y}}switch(b.split("-")[1]){case "start":f[a]-=h*(c&&l?-1:1);break;case "end":f[a]+=h*(c&&l?-1:1)}return f}
let ya=async(a,b,c)=>{const {placement:d="bottom",strategy:e="absolute",middleware:f=[],platform:g}=c;c=f.filter(Boolean);const h=await (null==g.isRTL?void 0:g.isRTL(b));let k=await g.getElementRects({reference:a,floating:b,strategy:e}),{x:l,y:p}=xa(k,d,h),q=d,n={},t=0;for(let r=0;r<c.length;r++){const {name:m,fn:y}=c[r],{x:w,y:x,data:v,reset:u}=await y({x:l,y:p,initialPlacement:d,placement:q,strategy:e,middlewareData:n,rects:k,platform:g,elements:{reference:a,floating:b}});l=null!=w?w:l;p=null!=
x?x:p;n=A({},n,{[m]:A({},n[m],v)});u&&50>=t&&(t++,"object"===typeof u&&(u.placement&&(q=u.placement),u.rects&&(k=!0===u.rects?await g.getElementRects({reference:a,floating:b,strategy:e}):u.rects),{x:l,y:p}=xa(k,q,h)),r=-1)}return{x:l,y:p,placement:q,strategy:e,middlewareData:n}};function za(a){return"number"!==typeof a?A({top:0,right:0,bottom:0,left:0},a):{top:a,right:a,bottom:a,left:a}}function Aa(a){return A({},a,{top:a.y,left:a.x,right:a.x+a.width,bottom:a.y+a.height})}
async function Ba(a,b){var c;void 0===b&&(b={});let {x:d,y:e,platform:f,rects:g,elements:h,strategy:k}=a,{boundary:l="clippingAncestors",rootBoundary:p="viewport",elementContext:q="floating",altBoundary:n=!1,padding:t=0}=b;a=za(t);b=h[n?"floating"===q?"reference":"floating":q];b=Aa(await f.getClippingRect({element:(null!=(c=await (null==f.isElement?void 0:f.isElement(b)))?c:1)?b:b.contextElement||await (null==f.getDocumentElement?void 0:f.getDocumentElement(h.floating)),boundary:l,rootBoundary:p,
strategy:k}));var r="floating"===q?A({},g.floating,{x:d,y:e}):g.reference;let m=await (null==f.getOffsetParent?void 0:f.getOffsetParent(h.floating));c=await (null==f.isElement?void 0:f.isElement(m))?await (null==f.getScale?void 0:f.getScale(m))||{x:1,y:1}:{x:1,y:1};r=Aa(f.convertOffsetParentRelativeRectToViewportRelativeRect?await f.convertOffsetParentRelativeRectToViewportRelativeRect({rect:r,offsetParent:m,strategy:k}):r);return{top:(b.top-r.top+a.top)/c.y,bottom:(r.bottom-b.bottom+a.bottom)/c.y,
left:(b.left-r.left+a.left)/c.x,right:(r.right-b.right+a.right)/c.x}}
let Ca=Math.min,Da=Math.max,Ea=a=>({name:"arrow",options:a,async fn(b){const {element:c,padding:d=0}=a||{},{x:e,y:f,placement:g,rects:h,platform:k,elements:l}=b;if(null==c)return{};var p=za(d);b={x:e,y:f};const q=wa(g),n=va(q),t=await k.getDimensions(c);var r="y"===q;const m=r?"top":"left",y=r?"bottom":"right";r=r?"clientHeight":"clientWidth";var w=h.reference[n]+h.reference[q]-b[q]-h.floating[n],x=b[q]-h.reference[q],v=await (null==k.getOffsetParent?void 0:k.getOffsetParent(c));let u=v?v[r]:0;u&&
await (null==k.isElement?void 0:k.isElement(v))||(u=l.floating[r]||h.floating[n]);r=p[m];v=u-t[n]-p[y];w=u/2-t[n]/2+(w/2-x/2);x=Da(r,Ca(w,v));p=null!=g.split("-")[1]&&w!=x&&0>h.reference[n]/2-(w<r?p[m]:p[y])-t[n]/2;return{[q]:b[q]-(p?w<r?r-w:v-w:0),data:{[q]:x,centerOffset:w-x}}}}),Fa={left:"right",right:"left",bottom:"top",top:"bottom"};function Ga(a){return a.replace(/left|right|bottom|top/g,b=>Fa[b])}
function Ha(a,b,c){void 0===c&&(c=!1);let d=a.split("-")[1],e=wa(a);a=va(e);c="x"===e?d===(c?"end":"start")?"right":"left":"start"===d?"bottom":"top";b.reference[a]>b.floating[a]&&(c=Ga(c));return{main:c,cross:Ga(c)}}let Ia={start:"end",end:"start"};function Ja(a){return a.replace(/start|end/g,b=>Ia[b])}function Ka(a){let b=Ga(a);return[Ja(a),b,Ja(b)]}
function La(a,b,c){let d=["left","right"],e=["right","left"],f=["top","bottom"],g=["bottom","top"];switch(a){case "top":case "bottom":return c?b?e:d:b?d:e;case "left":case "right":return b?f:g;default:return[]}}function Ma(a,b,c,d){let e=a.split("-")[1];a=La(C(a),"start"===c,d);e&&(a=a.map(f=>f+"-"+e),b&&(a=a.concat(a.map(Ja))));return a}
function Na(a){void 0===a&&(a={});return{name:"flip",options:a,async fn(b){var c;const {placement:d,middlewareData:e,rects:f,initialPlacement:g,platform:h,elements:k}=b,{mainAxis:l=!0,crossAxis:p=!0,fallbackPlacements:q,fallbackStrategy:n="bestFit",fallbackAxisSideDirection:t="none",flipAlignment:r=!0}=a;var m=sa(a,ta);const y=C(d);var w=C(g)===g;const x=await (null==h.isRTL?void 0:h.isRTL(k.floating));w=q||(w||!r?[Ga(g)]:Ka(g));q||"none"===t||w.push(...Ma(g,r,t,x));w=[g,...w];m=await Ba(b,m);const v=
[];b=(null==(c=e.flip)?void 0:c.overflows)||[];l&&v.push(m[y]);if(p){const {main:D,cross:E}=Ha(d,f,x);v.push(m[D],m[E])}b=[...b,{placement:d,overflows:v}];if(!v.every(D=>0>=D)){var u,B;c=((null==(u=e.flip)?void 0:u.index)||0)+1;if(u=w[c])return{data:{index:c,overflows:b},reset:{placement:u}};u=null==(B=b.filter(D=>0>=D.overflows[0]).sort((D,E)=>D.overflows[1]-E.overflows[1])[0])?void 0:B.placement;if(!u)switch(n){case "bestFit":var L;(B=null==(L=b.map(D=>[D.placement,D.overflows.filter(E=>0<E).reduce((E,
N)=>E+N,0)]).sort((D,E)=>D[1]-E[1])[0])?void 0:L[0])&&(u=B);break;case "initialPlacement":u=g}if(d!==u)return{reset:{placement:u}}}return{}}}}
function Oa(a){void 0===a&&(a={});return{name:"shift",options:a,async fn(b){const {x:c,y:d,placement:e}=b,{mainAxis:f=!0,crossAxis:g=!1,limiter:h={fn:t=>{let {x:r,y:m}=t;return{x:r,y:m}}}}=a;var k=sa(a,ua),l={x:c,y:d};k=await Ba(b,k);const p=wa(C(e)),q="x"===p?"y":"x";let n=l[p];l=l[q];f&&(n=Da(n+k["y"===p?"top":"left"],Ca(n,n-k["y"===p?"bottom":"right"])));g&&(l=Da(l+k["y"===q?"top":"left"],Ca(l,l-k["y"===q?"bottom":"right"])));b=h.fn(A({},b,{[p]:n,[q]:l}));return A({},b,{data:{x:b.x-c,y:b.y-d}})}}}
function Pa(a){void 0===a&&(a={});return{options:a,fn(b){const {x:c,y:d,placement:e,rects:f,middlewareData:g}=b,{offset:h=0,mainAxis:k=!0,crossAxis:l=!0}=a;var p={x:c,y:d};const q=wa(e),n="x"===q?"y":"x";let t=p[q];p=p[n];b="function"===typeof h?h(b):h;b="number"===typeof b?{mainAxis:b,crossAxis:0}:A({mainAxis:0,crossAxis:0},b);if(k){var r="y"===q?"height":"width",m=f.reference[q]-f.floating[r]+b.mainAxis;r=f.reference[q]+f.reference[r]-b.mainAxis;t<m?t=m:t>r&&(t=r)}if(l){var y,w;r="y"===q?"width":
"height";const x=["top","left"].includes(C(e));m=f.reference[n]-f.floating[r]+(x?(null==(y=g.offset)?void 0:y[n])||0:0)+(x?0:b.crossAxis);y=f.reference[n]+f.reference[r]+(x?0:(null==(w=g.offset)?void 0:w[n])||0)-(x?b.crossAxis:0);p<m?p=m:p>y&&(p=y)}return{[q]:t,[n]:p}}}}function F(a){var b;return(null==(b=a.ownerDocument)?void 0:b.defaultView)||window}function G(a){return F(a).getComputedStyle(a)}function H(a){return a instanceof F(a).Node?(a.nodeName||"").toLowerCase():""}let Qa;
function Ra(){if(Qa)return Qa;let a=navigator.userAgentData;return a&&Array.isArray(a.brands)?Qa=a.brands.map(b=>b.brand+"/"+b.version).join(" "):navigator.userAgent}function I(a){return a instanceof F(a).HTMLElement}function J(a){return a instanceof F(a).Element}function Sa(a){if("undefined"===typeof ShadowRoot)return!1;let b=F(a).ShadowRoot;return a instanceof b||a instanceof ShadowRoot}
function Ta(a){let {overflow:b,overflowX:c,overflowY:d,display:e}=G(a);return/auto|scroll|overlay|hidden|clip/.test(b+d+c)&&!["inline","contents"].includes(e)}
function Ua(a){let b=/firefox/i.test(Ra()),c=G(a);a=c.backdropFilter||c.WebkitBackdropFilter;return"none"!==c.transform||"none"!==c.perspective||(a?"none"!==a:!1)||b&&"filter"===c.willChange||b&&(c.filter?"none"!==c.filter:!1)||["transform","perspective"].some(d=>c.willChange.includes(d))||["paint","layout","strict","content"].some(d=>{let e=c.contain;return null!=e?e.includes(d):!1})}function Va(){return/^((?!chrome|android).)*safari/i.test(Ra())}
function Wa(a){return["html","body","#document"].includes(H(a))}let Xa=Math.min,Ya=Math.max,Za=Math.round;function $a(a){var b=G(a);let c=parseFloat(b.width);b=parseFloat(b.height);var d=I(a);let e=d?a.offsetWidth:c;a=d?a.offsetHeight:b;if(d=Za(c)!==e||Za(b)!==a)c=e,b=a;return{width:c,height:b,fallback:d}}function ab(a){return J(a)?a:a.contextElement}let bb={x:1,y:1};
function cb(a){var b=ab(a);if(!I(b))return bb;a=b.getBoundingClientRect();let {width:c,height:d,fallback:e}=$a(b);b=(e?Za(a.width):a.width)/c;a=(e?Za(a.height):a.height)/d;b&&Number.isFinite(b)||(b=1);a&&Number.isFinite(a)||(a=1);return{x:b,y:a}}
function K(a,b,c,d){var e,f;void 0===b&&(b=!1);void 0===c&&(c=!1);var g=a.getBoundingClientRect(),h=ab(a),k=bb;b&&(d?J(d)&&(k=cb(d)):k=cb(a));a=h?F(h):window;b=Va()&&c;c=(g.left+(b?(null==(e=a.visualViewport)?void 0:e.offsetLeft)||0:0))/k.x;e=(g.top+(b?(null==(f=a.visualViewport)?void 0:f.offsetTop)||0:0))/k.y;f=g.width/k.x;g=g.height/k.y;if(h)for(h=F(h),k=d&&J(d)?F(d):d,a=h.frameElement;a&&d&&k!==h;){b=cb(a);let l=a.getBoundingClientRect(),p=getComputedStyle(a);l.x+=(a.clientLeft+parseFloat(p.paddingLeft))*
b.x;l.y+=(a.clientTop+parseFloat(p.paddingTop))*b.y;c*=b.x;e*=b.y;f*=b.x;g*=b.y;c+=l.x;e+=l.y;a=F(a).frameElement}return Aa({width:f,height:g,x:c,y:e})}function M(a){return((a instanceof F(a).Node?a.ownerDocument:a.document)||window.document).documentElement}function db(a){return J(a)?{scrollLeft:a.scrollLeft,scrollTop:a.scrollTop}:{scrollLeft:a.pageXOffset,scrollTop:a.pageYOffset}}function eb(a){return K(M(a)).left+db(a).scrollLeft}
function fb(a){if("html"===H(a))return a;a=a.assignedSlot||a.parentNode||Sa(a)&&a.host||M(a);return Sa(a)?a.host:a}function gb(a){a=fb(a);return Wa(a)?a.ownerDocument.body:I(a)&&Ta(a)?a:gb(a)}function hb(a,b){var c;void 0===b&&(b=[]);let d=gb(a);a=d===(null==(c=a.ownerDocument)?void 0:c.body);c=F(d);return a?b.concat(c,c.visualViewport||[],Ta(d)?d:[]):b.concat(d,hb(d))}
function ib(a,b,c){if("viewport"===b){b=F(a);var d=M(a);b=b.visualViewport;a=d.clientWidth;d=d.clientHeight;var e=0,f=0;if(b){a=b.width;d=b.height;let g=Va();if(!g||g&&"fixed"===c)e=b.offsetLeft,f=b.offsetTop}c={width:a,height:d,x:e,y:f}}else"document"===b?(f=M(a),c=M(f),e=db(f),b=f.ownerDocument.body,a=Ya(c.scrollWidth,c.clientWidth,b.scrollWidth,b.clientWidth),d=Ya(c.scrollHeight,c.clientHeight,b.scrollHeight,b.clientHeight),f=-e.scrollLeft+eb(f),e=-e.scrollTop,"rtl"===G(b).direction&&(f+=Ya(c.clientWidth,
b.clientWidth)-a),c={width:a,height:d,x:f,y:e}):J(b)?(a=K(b,!0,"fixed"===c),c=a.top+b.clientTop,a=a.left+b.clientLeft,d=I(b)?cb(b):{x:1,y:1},c={width:b.clientWidth*d.x,height:b.clientHeight*d.y,x:a*d.x,y:c*d.y}):(c=A({},b),Va()&&(b=F(a),c.x-=(null==(d=b.visualViewport)?void 0:d.offsetLeft)||0,c.y-=(null==(e=b.visualViewport)?void 0:e.offsetTop)||0));return Aa(c)}
function jb(a,b){var c=b.get(a);if(c)return c;c=hb(a).filter(g=>J(g)&&"body"!==H(g));let d=null,e="fixed"===G(a).position,f=e?fb(a):a;for(;J(f)&&!Wa(f);){let g=G(f),h=Ua(f);"fixed"===g.position?d=null:(e?h||d:h||"static"!==g.position||!d||!["absolute","fixed"].includes(d.position))?d=g:c=c.filter(k=>k!==f);f=fb(f)}b.set(a,c);return c}function kb(a,b){return I(a)&&"fixed"!==G(a).position?b?b(a):a.offsetParent:null}
function lb(a,b){let c=F(a);if(!I(a))return c;let d=kb(a,b);for(;d&&["table","td","th"].includes(H(d))&&"static"===G(d).position;)d=kb(d,b);if(d&&("html"===H(d)||"body"===H(d)&&"static"===G(d).position&&!Ua(d)))return c;if(!(b=d))a:{for(a=fb(a);I(a)&&!Wa(a);)if(Ua(a)){b=a;break a}else a=fb(a);b=null}return b||c}
let mb={getClippingRect:function(a){let {element:b,boundary:c,rootBoundary:d,strategy:e}=a;a=[...("clippingAncestors"===c?jb(b,this._c):[].concat(c)),d];a=a.reduce((f,g)=>{g=ib(b,g,e);f.top=Ya(g.top,f.top);f.right=Xa(g.right,f.right);f.bottom=Xa(g.bottom,f.bottom);f.left=Ya(g.left,f.left);return f},ib(b,a[0],e));return{width:a.right-a.left,height:a.bottom-a.top,x:a.left,y:a.top}},convertOffsetParentRelativeRectToViewportRelativeRect:function(a){let {rect:b,offsetParent:c,strategy:d}=a;var e=I(c);
let f=M(c);if(c===f)return b;a={scrollLeft:0,scrollTop:0};let g={x:1,y:1};var h=0,k=0;if(e||!e&&"fixed"!==d){if("body"!==H(c)||Ta(f))a=db(c);I(c)&&(e=K(c),g=cb(c),h=e.x+c.clientLeft,k=e.y+c.clientTop)}return{width:b.width*g.x,height:b.height*g.y,x:b.x*g.x-a.scrollLeft*g.x+h,y:b.y*g.y-a.scrollTop*g.y+k}},isElement:J,getDimensions:function(a){return $a(a)},getOffsetParent:lb,getDocumentElement:M,getScale:cb,async getElementRects(a){let {reference:b,floating:c,strategy:d}=a;a=this.getDimensions;var e=
await (this.getOffsetParent||lb)(c),f=I(e);let g=M(e),h=K(b,!0,"fixed"===d,e),k={scrollLeft:0,scrollTop:0};var l=0,p=0;if(f||!f&&"fixed"!==d){if("body"!==H(e)||Ta(g))k=db(e);I(e)?(f=K(e,!0),l=f.x+e.clientLeft,p=f.y+e.clientTop):g&&(l=eb(g))}return{reference:{x:h.left+k.scrollLeft-l,y:h.top+k.scrollTop-p,width:h.width,height:h.height},floating:A({x:0,y:0},await a(c))}},getClientRects:a=>Array.from(a.getClientRects()),isRTL:a=>"rtl"===G(a).direction};
function nb(a,b,c,d){function e(){let r=K(a);!t||r.x===t.x&&r.y===t.y&&r.width===t.width&&r.height===t.height||c();t=r;n=requestAnimationFrame(e)}void 0===d&&(d={});let {ancestorScroll:f=!0,ancestorResize:g=!0,elementResize:h=!0,animationFrame:k=!1}=d,l=f&&!k,p=l||g?[...(J(a)?hb(a):a.contextElement?hb(a.contextElement):[]),...hb(b)]:[];p.forEach(r=>{l&&r.addEventListener("scroll",c,{passive:!0});g&&r.addEventListener("resize",c)});let q=null;h&&(q=new ResizeObserver(()=>{c()}),J(a)&&!k&&q.observe(a),
J(a)||!a.contextElement||k||q.observe(a.contextElement),q.observe(b));let n,t=k?K(a):null;k&&e();c();return()=>{var r;p.forEach(m=>{l&&m.removeEventListener("scroll",c);g&&m.removeEventListener("resize",c)});null==(r=q)?void 0:r.disconnect();q=null;k&&cancelAnimationFrame(n)}}let ob=(a,b,c)=>{var d=new Map;c=A({platform:mb},c);d=A({},c.platform,{_c:d});return ya(a,b,A({},c,{platform:d}))};
function pb(a){a.cleanup&&a.cleanup();let b=a._getResolvedAttachToOptions(),c=b.element,d=qb(b,a),e=void 0===b||null===b?!0:!b.element||!b.on;e&&(c=document.body,a.shepherdElementComponent.getElement().classList.add("shepherd-centered"));a.cleanup=nb(c,a.el,()=>{a.el?rb(c,a,d,e):a.cleanup()});a.target=b.element;return d}function rb(a,b,c,d){return ob(a,b.el,c).then(sb(b,d)).then(e=>new Promise(f=>{setTimeout(()=>f(e),300)})).then(e=>{e&&e.el&&e.el.focus({preventScroll:!0})})}
function sb(a,b){return c=>{let {x:d,y:e,placement:f,middlewareData:g}=c;if(!a.el)return a;b?Object.assign(a.el.style,{position:"fixed",left:"50%",top:"50%",transform:"translate(-50%, -50%)"}):Object.assign(a.el.style,{position:"absolute",left:`${d}px`,top:`${e}px`});a.el.dataset.popperPlacement=f;if((c=a.el.querySelector(".shepherd-arrow"))&&g.arrow){let {x:h,y:k}=g.arrow;Object.assign(c.style,{left:null!=h?`${h}px`:"",top:null!=k?`${k}px`:""})}return a}}
function qb(a,b){let c={strategy:"absolute",middleware:[]};var d=b.options.arrow&&b.el?b.el.querySelector(".shepherd-arrow"):!1;void 0!==a&&null!==a&&a.element&&a.on&&(c.middleware.push(Na(),Oa({limiter:Pa(),crossAxis:!0})),d&&c.middleware.push(Ea({element:d})),c.placement=a.on);return ka(b.options.floatingUIOptions||{},c)}function O(){}function tb(a,b){for(let c in b)a[c]=b[c];return a}function ub(a){return a()}function vb(a){return"function"===typeof a}
function P(a,b){return a!=a?b==b:a!==b||a&&"object"===typeof a||"function"===typeof a}function Q(a){a.parentNode&&a.parentNode.removeChild(a)}function wb(a){return document.createElementNS("http://www.w3.org/2000/svg",a)}function xb(a,b,c,d){a.addEventListener(b,c,d);return()=>a.removeEventListener(b,c,d)}function R(a,b,c){null==c?a.removeAttribute(b):a.getAttribute(b)!==c&&a.setAttribute(b,c)}
function yb(a,b){let c=Object.getOwnPropertyDescriptors(a.__proto__);for(let d in b)null==b[d]?a.removeAttribute(d):"style"===d?a.style.cssText=b[d]:"__value"===d?a.value=a[d]=b[d]:c[d]&&c[d].set?a[d]=b[d]:R(a,d,b[d])}function zb(a,b,c){a.classList[c?"add":"remove"](b)}let S;function Ab(){if(!S)throw Error("Function called outside component initialization");return S}let Bb=[],Cb=[],Db=[],Eb=[],Fb=Promise.resolve(),Gb=!1;function Hb(a){Db.push(a)}let Ib=new Set,Jb=0;
function Kb(){if(0===Jb){var a=S;do{try{for(;Jb<Bb.length;){let c=Bb[Jb];Jb++;S=c;var b=c.$$;if(null!==b.fragment){b.update();b.before_update.forEach(ub);let d=b.dirty;b.dirty=[-1];b.fragment&&b.fragment.p(b.ctx,d);b.after_update.forEach(Hb)}}}catch(c){throw Jb=Bb.length=0,c;}S=null;for(Jb=Bb.length=0;Cb.length;)Cb.pop()();for(let c=0;c<Db.length;c+=1){let d=Db[c];Ib.has(d)||(Ib.add(d),d())}Db.length=0}while(Bb.length);for(;Eb.length;)Eb.pop()();Gb=!1;Ib.clear();S=a}}
function Lb(a){let b=[],c=[];Db.forEach(d=>-1===a.indexOf(d)?b.push(d):c.push(d));c.forEach(d=>d());Db=b}let Mb=new Set,T;function Nb(){T={r:0,c:[],p:T}}function Ob(){T.r||T.c.forEach(ub);T=T.p}function U(a,b){a&&a.i&&(Mb.delete(a),a.i(b))}function V(a,b,c,d){a&&a.o?Mb.has(a)||(Mb.add(a),T.c.push(()=>{Mb.delete(a);d&&(c&&a.d(1),d())}),a.o(b)):d&&d()}function Pb(a){a&&a.c()}
function W(a,b,c,d){let {fragment:e,after_update:f}=a.$$;e&&e.m(b,c);d||Hb(()=>{let g=a.$$.on_mount.map(ub).filter(vb);a.$$.on_destroy?a.$$.on_destroy.push(...g):g.forEach(ub);a.$$.on_mount=[]});f.forEach(Hb)}function X(a,b){a=a.$$;null!==a.fragment&&(Lb(a.after_update),a.on_destroy.forEach(ub),a.fragment&&a.fragment.d(b),a.on_destroy=a.fragment=null,a.ctx=[])}
function Y(a,b,c,d,e,f,g,h){void 0===h&&(h=[-1]);let k=S;S=a;let l=a.$$={fragment:null,ctx:[],props:f,update:O,not_equal:e,bound:Object.create(null),on_mount:[],on_destroy:[],on_disconnect:[],before_update:[],after_update:[],context:new Map(b.context||(k?k.$$.context:[])),callbacks:Object.create(null),dirty:h,skip_bound:!1,root:b.target||k.$$.root};g&&g(l.root);let p=!1;l.ctx=c?c(a,b.props||{},function(q,n){let t=(2>=arguments.length?0:arguments.length-2)?2>=arguments.length?void 0:arguments[2]:n;
if(l.ctx&&e(l.ctx[q],l.ctx[q]=t)){if(!l.skip_bound&&l.bound[q])l.bound[q](t);p&&(-1===a.$$.dirty[0]&&(Bb.push(a),Gb||(Gb=!0,Fb.then(Kb)),a.$$.dirty.fill(0)),a.$$.dirty[q/31|0]|=1<<q%31)}return n}):[];l.update();p=!0;l.before_update.forEach(ub);l.fragment=d?d(l.ctx):!1;b.target&&(b.hydrate?(c=Array.from(b.target.childNodes),l.fragment&&l.fragment.l(c),c.forEach(Q)):l.fragment&&l.fragment.c(),b.intro&&U(a.$$.fragment),W(a,b.target,b.anchor,b.customElement),Kb());S=k}
class Z{$destroy(){X(this,1);this.$destroy=O}$on(a,b){if(!vb(b))return O;let c=this.$$.callbacks[a]||(this.$$.callbacks[a]=[]);c.push(b);return()=>{let d=c.indexOf(b);-1!==d&&c.splice(d,1)}}$set(a){this.$$set&&0!==Object.keys(a).length&&(this.$$.skip_bound=!0,this.$$set(a),this.$$.skip_bound=!1)}}
function Qb(a){let b,c,d,e,f;return{c(){b=document.createElement("button");R(b,"aria-label",c=a[3]?a[3]:null);R(b,"class",d=`${a[1]||""} shepherd-button ${a[4]?"shepherd-button-secondary":""}`);b.disabled=a[2];R(b,"tabindex","0")},m(g,h){g.insertBefore(b,h||null);b.innerHTML=a[5];e||(f=xb(b,"click",function(){vb(a[0])&&a[0].apply(this,arguments)}),e=!0)},p(g,h){[h]=h;a=g;h&32&&(b.innerHTML=a[5]);h&8&&c!==(c=a[3]?a[3]:null)&&R(b,"aria-label",c);h&18&&d!==(d=`${a[1]||""} shepherd-button ${a[4]?"shepherd-button-secondary":
""}`)&&R(b,"class",d);h&4&&(b.disabled=a[2])},i:O,o:O,d(g){g&&Q(b);e=!1;f()}}}function Rb(a,b,c){function d(n){return z(n)?n.call(f):n}let {config:e,step:f}=b,g,h,k,l,p,q;a.$$set=n=>{"config"in n&&c(6,e=n.config);"step"in n&&c(7,f=n.step)};a.$$.update=()=>{a.$$.dirty&192&&(c(0,g=e.action?e.action.bind(f.tour):null),c(1,h=e.classes),c(2,k=e.disabled?d(e.disabled):!1),c(3,l=e.label?d(e.label):null),c(4,p=e.secondary),c(5,q=e.text?d(e.text):null))};return[g,h,k,l,p,q,e,f]}
class Sb extends Z{constructor(a){super();Y(this,a,Rb,Qb,P,{config:6,step:7})}}function Tb(a,b,c){a=a.slice();a[2]=b[c];return a}
function Ub(a){let b,c,d=a[1],e=[];for(let g=0;g<d.length;g+=1)e[g]=Vb(Tb(a,d,g));let f=g=>V(e[g],1,1,()=>{e[g]=null});return{c(){for(let g=0;g<e.length;g+=1)e[g].c();b=document.createTextNode("")},m(g,h){for(let k=0;k<e.length;k+=1)e[k]&&e[k].m(g,h);g.insertBefore(b,h||null);c=!0},p(g,h){if(h&3){d=g[1];let k;for(k=0;k<d.length;k+=1){let l=Tb(g,d,k);e[k]?(e[k].p(l,h),U(e[k],1)):(e[k]=Vb(l),e[k].c(),U(e[k],1),e[k].m(b.parentNode,b))}Nb();for(k=d.length;k<e.length;k+=1)f(k);Ob()}},i(){if(!c){for(let g=
0;g<d.length;g+=1)U(e[g]);c=!0}},o(){e=e.filter(Boolean);for(let g=0;g<e.length;g+=1)V(e[g]);c=!1},d(g){var h=e;for(let k=0;k<h.length;k+=1)h[k]&&h[k].d(g);g&&Q(b)}}}function Vb(a){let b,c;b=new Sb({props:{config:a[2],step:a[0]}});return{c(){Pb(b.$$.fragment)},m(d,e){W(b,d,e);c=!0},p(d,e){let f={};e&2&&(f.config=d[2]);e&1&&(f.step=d[0]);b.$set(f)},i(d){c||(U(b.$$.fragment,d),c=!0)},o(d){V(b.$$.fragment,d);c=!1},d(d){X(b,d)}}}
function Wb(a){let b,c,d=a[1]&&Ub(a);return{c(){b=document.createElement("footer");d&&d.c();R(b,"class","shepherd-footer")},m(e,f){e.insertBefore(b,f||null);d&&d.m(b,null);c=!0},p(e,f){[f]=f;e[1]?d?(d.p(e,f),f&2&&U(d,1)):(d=Ub(e),d.c(),U(d,1),d.m(b,null)):d&&(Nb(),V(d,1,1,()=>{d=null}),Ob())},i(){c||(U(d),c=!0)},o(){V(d);c=!1},d(e){e&&Q(b);d&&d.d()}}}function Xb(a,b,c){let d,{step:e}=b;a.$$set=f=>{"step"in f&&c(0,e=f.step)};a.$$.update=()=>{a.$$.dirty&1&&c(1,d=e.options.buttons)};return[e,d]}
class Yb extends Z{constructor(a){super();Y(this,a,Xb,Wb,P,{step:0})}}
function Zb(a){let b,c,d,e,f;return{c(){b=document.createElement("button");c=document.createElement("span");c.textContent="\u00d7";R(c,"aria-hidden","true");R(b,"aria-label",d=a[0].label?a[0].label:"Close Tour");R(b,"class","shepherd-cancel-icon");R(b,"type","button")},m(g,h){g.insertBefore(b,h||null);b.appendChild(c);e||(f=xb(b,"click",a[1]),e=!0)},p(g,h){[h]=h;h&1&&d!==(d=g[0].label?g[0].label:"Close Tour")&&R(b,"aria-label",d)},i:O,o:O,d(g){g&&Q(b);e=!1;f()}}}
function $b(a,b,c){let {cancelIcon:d,step:e}=b;a.$$set=f=>{"cancelIcon"in f&&c(0,d=f.cancelIcon);"step"in f&&c(2,e=f.step)};return[d,f=>{f.preventDefault();e.cancel()},e]}class ac extends Z{constructor(a){super();Y(this,a,$b,Zb,P,{cancelIcon:0,step:2})}}function bc(a){let b;return{c(){b=document.createElement("h3");R(b,"id",a[1]);R(b,"class","shepherd-title")},m(c,d){c.insertBefore(b,d||null);a[3](b)},p(c,d){[d]=d;d&2&&R(b,"id",c[1])},i:O,o:O,d(c){c&&Q(b);a[3](null)}}}
function cc(a,b,c){let {labelId:d,element:e,title:f}=b;Ab().$$.after_update.push(()=>{z(f)&&c(2,f=f());c(0,e.innerHTML=f,e)});a.$$set=g=>{"labelId"in g&&c(1,d=g.labelId);"element"in g&&c(0,e=g.element);"title"in g&&c(2,f=g.title)};return[e,d,f,function(g){Cb[g?"unshift":"push"](()=>{e=g;c(0,e)})}]}class dc extends Z{constructor(a){super();Y(this,a,cc,bc,P,{labelId:1,element:0,title:2})}}
function ec(a){let b,c;b=new dc({props:{labelId:a[0],title:a[2]}});return{c(){Pb(b.$$.fragment)},m(d,e){W(b,d,e);c=!0},p(d,e){let f={};e&1&&(f.labelId=d[0]);e&4&&(f.title=d[2]);b.$set(f)},i(d){c||(U(b.$$.fragment,d),c=!0)},o(d){V(b.$$.fragment,d);c=!1},d(d){X(b,d)}}}
function fc(a){let b,c;b=new ac({props:{cancelIcon:a[3],step:a[1]}});return{c(){Pb(b.$$.fragment)},m(d,e){W(b,d,e);c=!0},p(d,e){let f={};e&8&&(f.cancelIcon=d[3]);e&2&&(f.step=d[1]);b.$set(f)},i(d){c||(U(b.$$.fragment,d),c=!0)},o(d){V(b.$$.fragment,d);c=!1},d(d){X(b,d)}}}
function gc(a){let b,c,d,e=a[2]&&ec(a),f=a[3]&&a[3].enabled&&fc(a);return{c(){b=document.createElement("header");e&&e.c();c=document.createTextNode(" ");f&&f.c();R(b,"class","shepherd-header")},m(g,h){g.insertBefore(b,h||null);e&&e.m(b,null);b.appendChild(c);f&&f.m(b,null);d=!0},p(g,h){[h]=h;g[2]?e?(e.p(g,h),h&4&&U(e,1)):(e=ec(g),e.c(),U(e,1),e.m(b,c)):e&&(Nb(),V(e,1,1,()=>{e=null}),Ob());g[3]&&g[3].enabled?f?(f.p(g,h),h&8&&U(f,1)):(f=fc(g),f.c(),U(f,1),f.m(b,null)):f&&(Nb(),V(f,1,1,()=>{f=null}),
Ob())},i(){d||(U(e),U(f),d=!0)},o(){V(e);V(f);d=!1},d(g){g&&Q(b);e&&e.d();f&&f.d()}}}function hc(a,b,c){let {labelId:d,step:e}=b,f,g;a.$$set=h=>{"labelId"in h&&c(0,d=h.labelId);"step"in h&&c(1,e=h.step)};a.$$.update=()=>{a.$$.dirty&2&&(c(2,f=e.options.title),c(3,g=e.options.cancelIcon))};return[d,e,f,g]}class ic extends Z{constructor(a){super();Y(this,a,hc,gc,P,{labelId:0,step:1})}}
function jc(a){let b;return{c(){b=document.createElement("div");R(b,"class","shepherd-text");R(b,"id",a[1])},m(c,d){c.insertBefore(b,d||null);a[3](b)},p(c,d){[d]=d;d&2&&R(b,"id",c[1])},i:O,o:O,d(c){c&&Q(b);a[3](null)}}}
function kc(a,b,c){let {descriptionId:d,element:e,step:f}=b;Ab().$$.after_update.push(()=>{let {text:g}=f.options;z(g)&&(g=g.call(f));g instanceof HTMLElement?e.appendChild(g):c(0,e.innerHTML=g,e)});a.$$set=g=>{"descriptionId"in g&&c(1,d=g.descriptionId);"element"in g&&c(0,e=g.element);"step"in g&&c(2,f=g.step)};return[e,d,f,function(g){Cb[g?"unshift":"push"](()=>{e=g;c(0,e)})}]}class lc extends Z{constructor(a){super();Y(this,a,kc,jc,P,{descriptionId:1,element:0,step:2})}}
function mc(a){let b,c;b=new ic({props:{labelId:a[1],step:a[2]}});return{c(){Pb(b.$$.fragment)},m(d,e){W(b,d,e);c=!0},p(d,e){let f={};e&2&&(f.labelId=d[1]);e&4&&(f.step=d[2]);b.$set(f)},i(d){c||(U(b.$$.fragment,d),c=!0)},o(d){V(b.$$.fragment,d);c=!1},d(d){X(b,d)}}}
function nc(a){let b,c;b=new lc({props:{descriptionId:a[0],step:a[2]}});return{c(){Pb(b.$$.fragment)},m(d,e){W(b,d,e);c=!0},p(d,e){let f={};e&1&&(f.descriptionId=d[0]);e&4&&(f.step=d[2]);b.$set(f)},i(d){c||(U(b.$$.fragment,d),c=!0)},o(d){V(b.$$.fragment,d);c=!1},d(d){X(b,d)}}}
function oc(a){let b,c;b=new Yb({props:{step:a[2]}});return{c(){Pb(b.$$.fragment)},m(d,e){W(b,d,e);c=!0},p(d,e){let f={};e&4&&(f.step=d[2]);b.$set(f)},i(d){c||(U(b.$$.fragment,d),c=!0)},o(d){V(b.$$.fragment,d);c=!1},d(d){X(b,d)}}}
function pc(a){let b,c=void 0!==a[2].options.title||a[2].options.cancelIcon&&a[2].options.cancelIcon.enabled,d,e=void 0!==a[2].options.text,f,g=Array.isArray(a[2].options.buttons)&&a[2].options.buttons.length,h,k=c&&mc(a),l=e&&nc(a),p=g&&oc(a);return{c(){b=document.createElement("div");k&&k.c();d=document.createTextNode(" ");l&&l.c();f=document.createTextNode(" ");p&&p.c();R(b,"class","shepherd-content")},m(q,n){q.insertBefore(b,n||null);k&&k.m(b,null);b.appendChild(d);l&&l.m(b,null);b.appendChild(f);
p&&p.m(b,null);h=!0},p(q,n){[n]=n;n&4&&(c=void 0!==q[2].options.title||q[2].options.cancelIcon&&q[2].options.cancelIcon.enabled);c?k?(k.p(q,n),n&4&&U(k,1)):(k=mc(q),k.c(),U(k,1),k.m(b,d)):k&&(Nb(),V(k,1,1,()=>{k=null}),Ob());n&4&&(e=void 0!==q[2].options.text);e?l?(l.p(q,n),n&4&&U(l,1)):(l=nc(q),l.c(),U(l,1),l.m(b,f)):l&&(Nb(),V(l,1,1,()=>{l=null}),Ob());n&4&&(g=Array.isArray(q[2].options.buttons)&&q[2].options.buttons.length);g?p?(p.p(q,n),n&4&&U(p,1)):(p=oc(q),p.c(),U(p,1),p.m(b,null)):p&&(Nb(),
V(p,1,1,()=>{p=null}),Ob())},i(){h||(U(k),U(l),U(p),h=!0)},o(){V(k);V(l);V(p);h=!1},d(q){q&&Q(b);k&&k.d();l&&l.d();p&&p.d()}}}function qc(a,b,c){let {descriptionId:d,labelId:e,step:f}=b;a.$$set=g=>{"descriptionId"in g&&c(0,d=g.descriptionId);"labelId"in g&&c(1,e=g.labelId);"step"in g&&c(2,f=g.step)};return[d,e,f]}class rc extends Z{constructor(a){super();Y(this,a,qc,pc,P,{descriptionId:0,labelId:1,step:2})}}
function sc(){let a;return{c(){a=document.createElement("div");R(a,"class","shepherd-arrow");R(a,"data-popper-arrow","")},m(b,c){b.insertBefore(a,c||null)},d(b){b&&Q(a)}}}
function tc(a){let b,c,d,e,f,g,h,k,l=a[4].options.arrow&&a[4].options.attachTo&&a[4].options.attachTo.element&&a[4].options.attachTo.on&&sc();d=new rc({props:{descriptionId:a[2],labelId:a[3],step:a[4]}});let p=[{"aria-describedby":e=void 0!==a[4].options.text?a[2]:null},{"aria-labelledby":f=a[4].options.title?a[3]:null},a[1],{role:"dialog"},{tabindex:"0"}],q={};for(let n=0;n<p.length;n+=1)q=tb(q,p[n]);return{c(){b=document.createElement("div");l&&l.c();c=document.createTextNode(" ");Pb(d.$$.fragment);
yb(b,q);zb(b,"shepherd-has-cancel-icon",a[5]);zb(b,"shepherd-has-title",a[6]);zb(b,"shepherd-element",!0)},m(n,t){n.insertBefore(b,t||null);l&&l.m(b,null);b.appendChild(c);W(d,b,null);a[13](b);g=!0;h||(k=xb(b,"keydown",a[7]),h=!0)},p(n,t){var [r]=t;n[4].options.arrow&&n[4].options.attachTo&&n[4].options.attachTo.element&&n[4].options.attachTo.on?l||(l=sc(),l.c(),l.m(b,c)):l&&(l.d(1),l=null);t={};r&4&&(t.descriptionId=n[2]);r&8&&(t.labelId=n[3]);r&16&&(t.step=n[4]);d.$set(t);t=b;r=[(!g||r&20&&e!==
(e=void 0!==n[4].options.text?n[2]:null))&&{"aria-describedby":e},(!g||r&24&&f!==(f=n[4].options.title?n[3]:null))&&{"aria-labelledby":f},r&2&&n[1],{role:"dialog"},{tabindex:"0"}];let m={},y={},w={$$scope:1},x=p.length;for(;x--;){let v=p[x],u=r[x];if(u){for(let B in v)B in u||(y[B]=1);for(let B in u)w[B]||(m[B]=u[B],w[B]=1);p[x]=u}else for(let B in v)w[B]=1}for(let v in y)v in m||(m[v]=void 0);yb(t,q=m);zb(b,"shepherd-has-cancel-icon",n[5]);zb(b,"shepherd-has-title",n[6]);zb(b,"shepherd-element",
!0)},i(n){g||(U(d.$$.fragment,n),g=!0)},o(n){V(d.$$.fragment,n);g=!1},d(n){n&&Q(b);l&&l.d();X(d);a[13](null);h=!1;k()}}}function uc(a){return a.split(" ").filter(b=>!!b.length)}
function vc(a,b,c){let {classPrefix:d,element:e,descriptionId:f,firstFocusableElement:g,focusableElements:h,labelId:k,lastFocusableElement:l,step:p,dataStepId:q}=b,n,t,r;Ab().$$.on_mount.push(()=>{c(1,q={[`data-${d}shepherd-step-id`]:p.id});c(9,h=e.querySelectorAll('a[href], area[href], input:not([disabled]), select:not([disabled]), textarea:not([disabled]), button:not([disabled]), [tabindex="0"]'));c(8,g=h[0]);c(10,l=h[h.length-1])});Ab().$$.after_update.push(()=>{if(r!==p.options.classes){var m=
r;la(m)&&(m=uc(m),m.length&&e.classList.remove(...m));m=r=p.options.classes;la(m)&&(m=uc(m),m.length&&e.classList.add(...m))}});a.$$set=m=>{"classPrefix"in m&&c(11,d=m.classPrefix);"element"in m&&c(0,e=m.element);"descriptionId"in m&&c(2,f=m.descriptionId);"firstFocusableElement"in m&&c(8,g=m.firstFocusableElement);"focusableElements"in m&&c(9,h=m.focusableElements);"labelId"in m&&c(3,k=m.labelId);"lastFocusableElement"in m&&c(10,l=m.lastFocusableElement);"step"in m&&c(4,p=m.step);"dataStepId"in m&&
c(1,q=m.dataStepId)};a.$$.update=()=>{a.$$.dirty&16&&(c(5,n=p.options&&p.options.cancelIcon&&p.options.cancelIcon.enabled),c(6,t=p.options&&p.options.title))};return[e,q,f,k,p,n,t,m=>{const {tour:y}=p;switch(m.keyCode){case 9:if(0===h.length){m.preventDefault();break}if(m.shiftKey){if(document.activeElement===g||document.activeElement.classList.contains("shepherd-element"))m.preventDefault(),l.focus()}else document.activeElement===l&&(m.preventDefault(),g.focus());break;case 27:y.options.exitOnEsc&&
p.cancel();break;case 37:y.options.keyboardNavigation&&y.back();break;case 39:y.options.keyboardNavigation&&y.next()}},g,h,l,d,()=>e,function(m){Cb[m?"unshift":"push"](()=>{e=m;c(0,e)})}]}class wc extends Z{constructor(a){super();Y(this,a,vc,tc,P,{classPrefix:11,element:0,descriptionId:2,firstFocusableElement:8,focusableElements:9,labelId:3,lastFocusableElement:10,step:4,dataStepId:1,getElement:12})}get getElement(){return this.$$.ctx[12]}}
class xc extends ma{constructor(a,b){void 0===b&&(b={});super(a,b);this.tour=a;this.classPrefix=this.tour.options?qa(this.tour.options.classPrefix):"";this.styles=a.styles;this._resolvedAttachTo=null;na(this);this._setOptions(b);return this}cancel(){this.tour.cancel();this.trigger("cancel")}complete(){this.tour.complete();this.trigger("complete")}destroy(){this.cleanup&&this.cleanup();this.cleanup=null;this.el instanceof HTMLElement&&(this.el.remove(),this.el=null);this._updateStepTargetOnHide();
this.trigger("destroy")}getTour(){return this.tour}hide(){this.tour.modal.hide();this.trigger("before-hide");this.el&&(this.el.hidden=!0);this._updateStepTargetOnHide();this.trigger("hide")}_resolveAttachToOptions(){let a=this.options.attachTo||{},b=Object.assign({},a);z(b.element)&&(b.element=b.element.call(this));if(la(b.element)){try{b.element=document.querySelector(b.element)}catch(c){}b.element||console.error(`The element for this Shepherd step was not found ${a.element}`)}return this._resolvedAttachTo=
b}_getResolvedAttachToOptions(){return null===this._resolvedAttachTo?this._resolveAttachToOptions():this._resolvedAttachTo}isOpen(){return!(!this.el||this.el.hidden)}show(){return z(this.options.beforeShowPromise)?Promise.resolve(this.options.beforeShowPromise()).then(()=>this._show()):Promise.resolve(this._show())}updateStepOptions(a){Object.assign(this.options,a);this.shepherdElementComponent&&this.shepherdElementComponent.$set({step:this})}getElement(){return this.el}getTarget(){return this.target}_createTooltipContent(){this.shepherdElementComponent=
new wc({target:this.tour.options.stepsContainer||document.body,props:{classPrefix:this.classPrefix,descriptionId:`${this.id}-description`,labelId:`${this.id}-label`,step:this,styles:this.styles}});return this.shepherdElementComponent.getElement()}_scrollTo(a){let {element:b}=this._getResolvedAttachToOptions();z(this.options.scrollToHandler)?this.options.scrollToHandler(b):b instanceof Element&&"function"===typeof b.scrollIntoView&&b.scrollIntoView(a)}_getClassOptions(a){var b=this.tour&&this.tour.options&&
this.tour.options.defaultStepOptions;b=b&&b.classes?b.classes:"";a=[...(a.classes?a.classes:"").split(" "),...b.split(" ")];a=new Set(a);return Array.from(a).join(" ").trim()}_setOptions(a){void 0===a&&(a={});let b=this.tour&&this.tour.options&&this.tour.options.defaultStepOptions;b=ka({},b||{});this.options=Object.assign({arrow:!0},b,a,{floatingUIOptions:ka(b.floatingUIOptions||{},a.floatingUIOptions||{})});let {when:c}=this.options;this.options.classes=this._getClassOptions(a);this.destroy();this.id=
this.options.id||`step-${ra()}`;c&&Object.keys(c).forEach(d=>{this.on(d,c[d],this)})}_setupElements(){void 0!==this.el&&this.destroy();this.el=this._createTooltipContent();this.options.advanceOn&&pa(this);pb(this)}_show(){this.trigger("before-show");this._resolveAttachToOptions();this._setupElements();this.tour.modal||this.tour._setupModal();this.tour.modal.setupForStep(this);this._styleTargetElementForStep(this);this.el.hidden=!1;this.options.scrollTo&&setTimeout(()=>{this._scrollTo(this.options.scrollTo)});
this.el.hidden=!1;let a=this.shepherdElementComponent.getElement(),b=this.target||document.body;b.classList.add(`${this.classPrefix}shepherd-enabled`);b.classList.add(`${this.classPrefix}shepherd-target`);a.classList.add("shepherd-enabled");this.trigger("show")}_styleTargetElementForStep(a){let b=a.target;b&&(a.options.highlightClass&&b.classList.add(a.options.highlightClass),b.classList.remove("shepherd-target-click-disabled"),!1===a.options.canClickTarget&&b.classList.add("shepherd-target-click-disabled"))}_updateStepTargetOnHide(){let a=
this.target||document.body;this.options.highlightClass&&a.classList.remove(this.options.highlightClass);a.classList.remove("shepherd-target-click-disabled",`${this.classPrefix}shepherd-enabled`,`${this.classPrefix}shepherd-target`)}}function yc(a){a&&({steps:a}=a,a.forEach(b=>{b.options&&!1===b.options.canClickTarget&&b.options.attachTo&&b.target instanceof HTMLElement&&b.target.classList.remove("shepherd-target-click-disabled")}))}
function zc(a){let b,c,d,e,f;return{c(){b=wb("svg");c=wb("path");R(c,"d",a[2]);R(b,"class",d=`${a[1]?"shepherd-modal-is-visible":""} shepherd-modal-overlay-container`)},m(g,h){g.insertBefore(b,h||null);b.appendChild(c);a[11](b);e||(f=xb(b,"touchmove",a[3]),e=!0)},p(g,h){[h]=h;h&4&&R(c,"d",g[2]);h&2&&d!==(d=`${g[1]?"shepherd-modal-is-visible":""} shepherd-modal-overlay-container`)&&R(b,"class",d)},i:O,o:O,d(g){g&&Q(b);a[11](null);e=!1;f()}}}
function Ac(a){if(!a)return null;let b=a instanceof HTMLElement&&window.getComputedStyle(a).overflowY;return"hidden"!==b&&"visible"!==b&&a.scrollHeight>=a.clientHeight?a:Ac(a.parentElement)}
function Bc(a,b,c){function d(){c(4,p={width:0,height:0,x:0,y:0,r:0})}function e(){c(1,q=!1);h()}function f(m,y,w,x){void 0===m&&(m=0);void 0===y&&(y=0);if(x){var v=x.getBoundingClientRect();let B=v.y||v.top;v=v.bottom||B+v.height;if(w){var u=w.getBoundingClientRect();w=u.y||u.top;u=u.bottom||w+u.height;B=Math.max(B,w);v=Math.min(v,u)}let {y:L,height:D}={y:B,height:Math.max(v-B,0)},{x:E,width:N,left:Ec}=x.getBoundingClientRect();c(4,p={width:N+2*m,height:D+2*m,x:(E||Ec)-m,y:L-m,r:y})}else d()}function g(){c(1,
q=!0)}function h(){n&&(cancelAnimationFrame(n),n=void 0);window.removeEventListener("touchmove",r,{passive:!1})}function k(m){let {modalOverlayOpeningPadding:y,modalOverlayOpeningRadius:w}=m.options,x=Ac(m.target),v=()=>{n=void 0;f(y,w,x,m.target);n=requestAnimationFrame(v)};v();window.addEventListener("touchmove",r,{passive:!1})}let {element:l,openingProperties:p}=b;ra();let q=!1,n=void 0,t;d();let r=m=>{m.preventDefault()};a.$$set=m=>{"element"in m&&c(0,l=m.element);"openingProperties"in m&&c(4,
p=m.openingProperties)};a.$$.update=()=>{if(a.$$.dirty&16){let {width:m,height:y,x:w=0,y:x=0,r:v=0}=p,{innerWidth:u,innerHeight:B}=window,{topLeft:L=0,topRight:D=0,bottomRight:E=0,bottomLeft:N=0}="number"===typeof v?{topLeft:v,topRight:v,bottomRight:v,bottomLeft:v}:v;c(2,t=`M${u},${B}\
H0\
V0\
H${u}\
V${B}\
Z\
M${w+L},${x}\
a${L},${L},0,0,0-${L},${L}\
V${y+x-N}\
a${N},${N},0,0,0,${N},${N}\
H${m+w-E}\
a${E},${E},0,0,0,${E}-${E}\
V${x+D}\
a${D},${D},0,0,0-${D}-${D}\
Z`)}};return[l,q,t,m=>{m.stopPropagation()},p,()=>l,d,e,f,function(m){h();m.tour.options.useModalOverlay?(k(m),g()):e()},g,function(m){Cb[m?"unshift":"push"](()=>{l=m;c(0,l)})}]}
class Cc extends Z{constructor(a){super();Y(this,a,Bc,zc,P,{element:0,openingProperties:4,getElement:5,closeModalOpening:6,hide:7,positionModal:8,setupForStep:9,show:10})}get getElement(){return this.$$.ctx[5]}get closeModalOpening(){return this.$$.ctx[6]}get hide(){return this.$$.ctx[7]}get positionModal(){return this.$$.ctx[8]}get setupForStep(){return this.$$.ctx[9]}get show(){return this.$$.ctx[10]}}let Dc=new ma;
class Fc extends ma{constructor(a){void 0===a&&(a={});super(a);na(this);this.options=Object.assign({},{exitOnEsc:!0,keyboardNavigation:!0},a);this.classPrefix=qa(this.options.classPrefix);this.steps=[];this.addSteps(this.options.steps);"active cancel complete inactive show start".split(" ").map(b=>{(c=>{this.on(c,d=>{d=d||{};d.tour=this;Dc.trigger(c,d)})})(b)});this._setTourID();return this}addStep(a,b){a instanceof xc?a.tour=this:a=new xc(this,a);void 0!==b?this.steps.splice(b,0,a):this.steps.push(a);
return a}addSteps(a){Array.isArray(a)&&a.forEach(b=>{this.addStep(b)});return this}back(){let a=this.steps.indexOf(this.currentStep);this.show(a-1,!1)}async cancel(){if(this.options.confirmCancel){let a=this.options.confirmCancelMessage||"Are you sure you want to stop the tour?";("function"===typeof this.options.confirmCancel?await this.options.confirmCancel():window.confirm(a))&&this._done("cancel")}else this._done("cancel")}complete(){this._done("complete")}getById(a){return this.steps.find(b=>
b.id===a)}getCurrentStep(){return this.currentStep}hide(){let a=this.getCurrentStep();if(a)return a.hide()}isActive(){return Dc.activeTour===this}next(){let a=this.steps.indexOf(this.currentStep);a===this.steps.length-1?this.complete():this.show(a+1,!0)}removeStep(a){let b=this.getCurrentStep();this.steps.some((c,d)=>{if(c.id===a)return c.isOpen()&&c.hide(),c.destroy(),this.steps.splice(d,1),!0});b&&b.id===a&&(this.currentStep=void 0,this.steps.length?this.show(0):this.cancel())}show(a,b){void 0===
a&&(a=0);void 0===b&&(b=!0);if(a=la(a)?this.getById(a):this.steps[a])this._updateStateBeforeShow(),z(a.options.showOn)&&!a.options.showOn()?this._skipStep(a,b):(this.trigger("show",{step:a,previous:this.currentStep}),this.currentStep=a,a.show())}start(){this.trigger("start");this.focusedElBeforeOpen=document.activeElement;this.currentStep=null;this._setupModal();this._setupActiveTour();this.next()}_done(a){let b=this.steps.indexOf(this.currentStep);Array.isArray(this.steps)&&this.steps.forEach(c=>
c.destroy());yc(this);this.trigger(a,{index:b});Dc.activeTour=null;this.trigger("inactive",{tour:this});this.modal&&this.modal.hide();"cancel"!==a&&"complete"!==a||!this.modal||(a=document.querySelector(".shepherd-modal-overlay-container"))&&a.remove();this.focusedElBeforeOpen instanceof HTMLElement&&this.focusedElBeforeOpen.focus()}_setupActiveTour(){this.trigger("active",{tour:this});Dc.activeTour=this}_setupModal(){this.modal=new Cc({target:this.options.modalContainer||document.body,props:{classPrefix:this.classPrefix,
styles:this.styles}})}_skipStep(a,b){a=this.steps.indexOf(a);a===this.steps.length-1?this.complete():this.show(b?a+1:a-1,b)}_updateStateBeforeShow(){this.currentStep&&this.currentStep.hide();this.isActive()||this._setupActiveTour()}_setTourID(){this.id=`${this.options.tourName||"tour"}--${ra()}`}}class Gc{constructor(){}}"undefined"===typeof window?Object.assign(Dc,{Tour:Gc,Step:Gc}):Object.assign(Dc,{Tour:Fc,Step:xc});export default Dc
//# sourceMappingURL=shepherd.esm.min.js.map
