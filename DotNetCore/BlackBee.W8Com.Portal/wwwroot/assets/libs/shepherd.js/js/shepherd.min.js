/*! shepherd.js 11.1.1 */

'use strict';(function(N,oa){"object"===typeof exports&&"undefined"!==typeof module?module.exports=oa():"function"===typeof define&&define.amd?define(oa):(N="undefined"!==typeof globalThis?globalThis:N||self,<PERSON><PERSON>=oa())})(this,function(){function N(a,b){return!1!==b.clone&&b.isMergeableObject(a)?fa(Array.isArray(a)?[]:{},a,b):a}function oa(a,b,c){return a.concat(b).map(function(d){return N(d,c)})}function Ab(a){return Object.getOwnPropertySymbols?Object.getOwnPropertySymbols(a).filter(function(b){return Object.propertyIsEnumerable.call(a,
b)}):[]}function Sa(a){return Object.keys(a).concat(Ab(a))}function Ta(a,b){try{return b in a}catch(c){return!1}}function Bb(a,b,c){var d={};c.isMergeableObject(a)&&Sa(a).forEach(function(e){d[e]=N(a[e],c)});Sa(b).forEach(function(e){if(!Ta(a,e)||Object.hasOwnProperty.call(a,e)&&Object.propertyIsEnumerable.call(a,e))if(Ta(a,e)&&c.isMergeableObject(b[e])){if(c.customMerge){var f=c.customMerge(e);f="function"===typeof f?f:fa}else f=fa;d[e]=f(a[e],b[e],c)}else d[e]=N(b[e],c)});return d}function fa(a,
b,c){c=c||{};c.arrayMerge=c.arrayMerge||oa;c.isMergeableObject=c.isMergeableObject||Cb;c.cloneUnlessOtherwiseSpecified=N;var d=Array.isArray(b),e=Array.isArray(a);return d!==e?N(b,c):d?c.arrayMerge(a,b,c):Bb(a,b,c)}function X(a){return"function"===typeof a}function pa(a){return"string"===typeof a}function Ua(a){let b=Object.getOwnPropertyNames(a.constructor.prototype);for(let c=0;c<b.length;c++){let d=b[c],e=a[d];"constructor"!==d&&"function"===typeof e&&(a[d]=e.bind(a))}return a}function Db(a,b){return c=>
{if(b.isOpen()){let d=b.el&&c.currentTarget===b.el;(void 0!==a&&c.currentTarget.matches(a)||d)&&b.tour.next()}}}function Eb(a){let {event:b,selector:c}=a.options.advanceOn||{};if(b){let d=Db(c,a),e;try{e=document.querySelector(c)}catch(f){}if(void 0===c||e)e?(e.addEventListener(b,d),a.on("destroy",()=>e.removeEventListener(b,d))):(document.body.addEventListener(b,d,!0),a.on("destroy",()=>document.body.removeEventListener(b,d,!0)));else return console.error(`No element was found for the selector supplied to advanceOn: ${c}`)}else return console.error("advanceOn was defined, but no event name was passed.")}
function Va(a){return pa(a)&&""!==a?"-"!==a.charAt(a.length-1)?`${a}-`:a:""}function Ca(){let a=Date.now();return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,b=>{let c=(a+16*Math.random())%16|0;a=Math.floor(a/16);return("x"==b?c:c&3|8).toString(16)})}function F(){F=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b],d;for(d in c)Object.prototype.hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a};return F.apply(this,arguments)}function Wa(a,
b){if(null==a)return{};var c={},d=Object.keys(a),e;for(e=0;e<d.length;e++){var f=d[e];0<=b.indexOf(f)||(c[f]=a[f])}return c}function Da(a){return"y"===a?"height":"width"}function Y(a){return a.split("-")[0]}function qa(a){return["top","bottom"].includes(Y(a))?"x":"y"}function Xa(a,b,c){let {reference:d,floating:e}=a;var f=d.x+d.width/2-e.width/2;let g=d.y+d.height/2-e.height/2;a=qa(b);var h=Da(a);h=d[h]/2-e[h]/2;let k=Y(b),l="x"===a;switch(k){case "top":f={x:f,y:d.y-e.height};break;case "bottom":f=
{x:f,y:d.y+d.height};break;case "right":f={x:d.x+d.width,y:g};break;case "left":f={x:d.x-e.width,y:g};break;default:f={x:d.x,y:d.y}}switch(b.split("-")[1]){case "start":f[a]-=h*(c&&l?-1:1);break;case "end":f[a]+=h*(c&&l?-1:1)}return f}function Ya(a){return"number"!==typeof a?F({top:0,right:0,bottom:0,left:0},a):{top:a,right:a,bottom:a,left:a}}function ua(a){return F({},a,{top:a.y,left:a.x,right:a.x+a.width,bottom:a.y+a.height})}async function Za(a,b){var c;void 0===b&&(b={});let {x:d,y:e,platform:f,
rects:g,elements:h,strategy:k}=a,{boundary:l="clippingAncestors",rootBoundary:p="viewport",elementContext:q="floating",altBoundary:n=!1,padding:t=0}=b;a=Ya(t);b=h[n?"floating"===q?"reference":"floating":q];b=ua(await f.getClippingRect({element:(null!=(c=await (null==f.isElement?void 0:f.isElement(b)))?c:1)?b:b.contextElement||await (null==f.getDocumentElement?void 0:f.getDocumentElement(h.floating)),boundary:l,rootBoundary:p,strategy:k}));var r="floating"===q?F({},g.floating,{x:d,y:e}):g.reference;
let m=await (null==f.getOffsetParent?void 0:f.getOffsetParent(h.floating));c=await (null==f.isElement?void 0:f.isElement(m))?await (null==f.getScale?void 0:f.getScale(m))||{x:1,y:1}:{x:1,y:1};r=ua(f.convertOffsetParentRelativeRectToViewportRelativeRect?await f.convertOffsetParentRelativeRectToViewportRelativeRect({rect:r,offsetParent:m,strategy:k}):r);return{top:(b.top-r.top+a.top)/c.y,bottom:(r.bottom-b.bottom+a.bottom)/c.y,left:(b.left-r.left+a.left)/c.x,right:(r.right-b.right+a.right)/c.x}}function va(a){return a.replace(/left|right|bottom|top/g,
b=>Fb[b])}function Gb(a,b,c){void 0===c&&(c=!1);let d=a.split("-")[1],e=qa(a);a=Da(e);c="x"===e?d===(c?"end":"start")?"right":"left":"start"===d?"bottom":"top";b.reference[a]>b.floating[a]&&(c=va(c));return{main:c,cross:va(c)}}function Ea(a){return a.replace(/start|end/g,b=>Hb[b])}function Ib(a){let b=va(a);return[Ea(a),b,Ea(b)]}function Jb(a,b,c){let d=["left","right"],e=["right","left"],f=["top","bottom"],g=["bottom","top"];switch(a){case "top":case "bottom":return c?b?e:d:b?d:e;case "left":case "right":return b?
f:g;default:return[]}}function Kb(a,b,c,d){let e=a.split("-")[1];a=Jb(Y(a),"start"===c,d);e&&(a=a.map(f=>f+"-"+e),b&&(a=a.concat(a.map(Ea))));return a}function G(a){var b;return(null==(b=a.ownerDocument)?void 0:b.defaultView)||window}function L(a){return G(a).getComputedStyle(a)}function T(a){return a instanceof G(a).Node?(a.nodeName||"").toLowerCase():""}function $a(){if(Fa)return Fa;let a=navigator.userAgentData;return a&&Array.isArray(a.brands)?Fa=a.brands.map(b=>b.brand+"/"+b.version).join(" "):
navigator.userAgent}function J(a){return a instanceof G(a).HTMLElement}function K(a){return a instanceof G(a).Element}function ab(a){if("undefined"===typeof ShadowRoot)return!1;let b=G(a).ShadowRoot;return a instanceof b||a instanceof ShadowRoot}function wa(a){let {overflow:b,overflowX:c,overflowY:d,display:e}=L(a);return/auto|scroll|overlay|hidden|clip/.test(b+d+c)&&!["inline","contents"].includes(e)}function Ga(a){let b=/firefox/i.test($a()),c=L(a);a=c.backdropFilter||c.WebkitBackdropFilter;return"none"!==
c.transform||"none"!==c.perspective||(a?"none"!==a:!1)||b&&"filter"===c.willChange||b&&(c.filter?"none"!==c.filter:!1)||["transform","perspective"].some(d=>c.willChange.includes(d))||["paint","layout","strict","content"].some(d=>{let e=c.contain;return null!=e?e.includes(d):!1})}function Ha(){return/^((?!chrome|android).)*safari/i.test($a())}function Ia(a){return["html","body","#document"].includes(T(a))}function bb(a){var b=L(a);let c=parseFloat(b.width);b=parseFloat(b.height);var d=J(a);let e=d?
a.offsetWidth:c;a=d?a.offsetHeight:b;if(d=xa(c)!==e||xa(b)!==a)c=e,b=a;return{width:c,height:b,fallback:d}}function cb(a){return K(a)?a:a.contextElement}function ha(a){var b=cb(a);if(!J(b))return db;a=b.getBoundingClientRect();let {width:c,height:d,fallback:e}=bb(b);b=(e?xa(a.width):a.width)/c;a=(e?xa(a.height):a.height)/d;b&&Number.isFinite(b)||(b=1);a&&Number.isFinite(a)||(a=1);return{x:b,y:a}}function Z(a,b,c,d){var e,f;void 0===b&&(b=!1);void 0===c&&(c=!1);var g=a.getBoundingClientRect(),h=cb(a),
k=db;b&&(d?K(d)&&(k=ha(d)):k=ha(a));a=h?G(h):window;b=Ha()&&c;c=(g.left+(b?(null==(e=a.visualViewport)?void 0:e.offsetLeft)||0:0))/k.x;e=(g.top+(b?(null==(f=a.visualViewport)?void 0:f.offsetTop)||0:0))/k.y;f=g.width/k.x;g=g.height/k.y;if(h)for(h=G(h),k=d&&K(d)?G(d):d,a=h.frameElement;a&&d&&k!==h;){b=ha(a);let l=a.getBoundingClientRect(),p=getComputedStyle(a);l.x+=(a.clientLeft+parseFloat(p.paddingLeft))*b.x;l.y+=(a.clientTop+parseFloat(p.paddingTop))*b.y;c*=b.x;e*=b.y;f*=b.x;g*=b.y;c+=l.x;e+=l.y;
a=G(a).frameElement}return ua({width:f,height:g,x:c,y:e})}function U(a){return((a instanceof G(a).Node?a.ownerDocument:a.document)||window.document).documentElement}function ya(a){return K(a)?{scrollLeft:a.scrollLeft,scrollTop:a.scrollTop}:{scrollLeft:a.pageXOffset,scrollTop:a.pageYOffset}}function eb(a){return Z(U(a)).left+ya(a).scrollLeft}function ra(a){if("html"===T(a))return a;a=a.assignedSlot||a.parentNode||ab(a)&&a.host||U(a);return ab(a)?a.host:a}function fb(a){a=ra(a);return Ia(a)?a.ownerDocument.body:
J(a)&&wa(a)?a:fb(a)}function sa(a,b){var c;void 0===b&&(b=[]);let d=fb(a);a=d===(null==(c=a.ownerDocument)?void 0:c.body);c=G(d);return a?b.concat(c,c.visualViewport||[],wa(d)?d:[]):b.concat(d,sa(d))}function gb(a,b,c){if("viewport"===b){b=G(a);var d=U(a);b=b.visualViewport;a=d.clientWidth;d=d.clientHeight;var e=0,f=0;if(b){a=b.width;d=b.height;let g=Ha();if(!g||g&&"fixed"===c)e=b.offsetLeft,f=b.offsetTop}c={width:a,height:d,x:e,y:f}}else"document"===b?(f=U(a),c=U(f),e=ya(f),b=f.ownerDocument.body,
a=ta(c.scrollWidth,c.clientWidth,b.scrollWidth,b.clientWidth),d=ta(c.scrollHeight,c.clientHeight,b.scrollHeight,b.clientHeight),f=-e.scrollLeft+eb(f),e=-e.scrollTop,"rtl"===L(b).direction&&(f+=ta(c.clientWidth,b.clientWidth)-a),c={width:a,height:d,x:f,y:e}):K(b)?(a=Z(b,!0,"fixed"===c),c=a.top+b.clientTop,a=a.left+b.clientLeft,d=J(b)?ha(b):{x:1,y:1},c={width:b.clientWidth*d.x,height:b.clientHeight*d.y,x:a*d.x,y:c*d.y}):(c=F({},b),Ha()&&(b=G(a),c.x-=(null==(d=b.visualViewport)?void 0:d.offsetLeft)||
0,c.y-=(null==(e=b.visualViewport)?void 0:e.offsetTop)||0));return ua(c)}function Lb(a,b){var c=b.get(a);if(c)return c;c=sa(a).filter(g=>K(g)&&"body"!==T(g));let d=null,e="fixed"===L(a).position,f=e?ra(a):a;for(;K(f)&&!Ia(f);){let g=L(f),h=Ga(f);"fixed"===g.position?d=null:(e?h||d:h||"static"!==g.position||!d||!["absolute","fixed"].includes(d.position))?d=g:c=c.filter(k=>k!==f);f=ra(f)}b.set(a,c);return c}function hb(a,b){return J(a)&&"fixed"!==L(a).position?b?b(a):a.offsetParent:null}function ib(a,
b){let c=G(a);if(!J(a))return c;let d=hb(a,b);for(;d&&["table","td","th"].includes(T(d))&&"static"===L(d).position;)d=hb(d,b);if(d&&("html"===T(d)||"body"===T(d)&&"static"===L(d).position&&!Ga(d)))return c;if(!(b=d))a:{for(a=ra(a);J(a)&&!Ia(a);)if(Ga(a)){b=a;break a}else a=ra(a);b=null}return b||c}function Mb(a,b,c,d){function e(){let r=Z(a);!t||r.x===t.x&&r.y===t.y&&r.width===t.width&&r.height===t.height||c();t=r;n=requestAnimationFrame(e)}void 0===d&&(d={});let {ancestorScroll:f=!0,ancestorResize:g=
!0,elementResize:h=!0,animationFrame:k=!1}=d,l=f&&!k,p=l||g?[...(K(a)?sa(a):a.contextElement?sa(a.contextElement):[]),...sa(b)]:[];p.forEach(r=>{l&&r.addEventListener("scroll",c,{passive:!0});g&&r.addEventListener("resize",c)});let q=null;h&&(q=new ResizeObserver(()=>{c()}),K(a)&&!k&&q.observe(a),K(a)||!a.contextElement||k||q.observe(a.contextElement),q.observe(b));let n,t=k?Z(a):null;k&&e();c();return()=>{var r;p.forEach(m=>{l&&m.removeEventListener("scroll",c);g&&m.removeEventListener("resize",
c)});null==(r=q)?void 0:r.disconnect();q=null;k&&cancelAnimationFrame(n)}}function Nb(a){a.cleanup&&a.cleanup();let b=a._getResolvedAttachToOptions(),c=b.element,d=Ob(b,a),e=void 0===b||null===b?!0:!b.element||!b.on;e&&(c=document.body,a.shepherdElementComponent.getElement().classList.add("shepherd-centered"));a.cleanup=Mb(c,a.el,()=>{a.el?Pb(c,a,d,e):a.cleanup()});a.target=b.element;return d}function Pb(a,b,c,d){return Qb(a,b.el,c).then(Rb(b,d)).then(e=>new Promise(f=>{setTimeout(()=>f(e),300)})).then(e=>
{e&&e.el&&e.el.focus({preventScroll:!0})})}function Rb(a,b){return c=>{let {x:d,y:e,placement:f,middlewareData:g}=c;if(!a.el)return a;b?Object.assign(a.el.style,{position:"fixed",left:"50%",top:"50%",transform:"translate(-50%, -50%)"}):Object.assign(a.el.style,{position:"absolute",left:`${d}px`,top:`${e}px`});a.el.dataset.popperPlacement=f;if((c=a.el.querySelector(".shepherd-arrow"))&&g.arrow){let {x:h,y:k}=g.arrow;Object.assign(c.style,{left:null!=h?`${h}px`:"",top:null!=k?`${k}px`:""})}return a}}
function Ob(a,b){let c={strategy:"absolute",middleware:[]};var d=b.options.arrow&&b.el?b.el.querySelector(".shepherd-arrow"):!1;void 0!==a&&null!==a&&a.element&&a.on&&(c.middleware.push(Sb(),Tb({limiter:Ub(),crossAxis:!0})),d&&c.middleware.push(Vb({element:d})),c.placement=a.on);return Ja(b.options.floatingUIOptions||{},c)}function H(){}function Wb(a,b){for(let c in b)a[c]=b[c];return a}function ia(a){return a()}function Ka(a){return"function"===typeof a}function O(a,b){return a!=a?b==b:a!==b||a&&
"object"===typeof a||"function"===typeof a}function I(a){a.parentNode&&a.parentNode.removeChild(a)}function jb(a){return document.createElementNS("http://www.w3.org/2000/svg",a)}function za(a,b,c,d){a.addEventListener(b,c,d);return()=>a.removeEventListener(b,c,d)}function A(a,b,c){null==c?a.removeAttribute(b):a.getAttribute(b)!==c&&a.setAttribute(b,c)}function kb(a,b){let c=Object.getOwnPropertyDescriptors(a.__proto__);for(let d in b)null==b[d]?a.removeAttribute(d):"style"===d?a.style.cssText=b[d]:
"__value"===d?a.value=a[d]=b[d]:c[d]&&c[d].set?a[d]=b[d]:A(a,d,b[d])}function ja(a,b,c){a.classList[c?"add":"remove"](b)}function Aa(){if(!P)throw Error("Function called outside component initialization");return P}function La(a){ka.push(a)}function lb(){if(0===la){var a=P;do{try{for(;la<ma.length;){let c=ma[la];la++;P=c;var b=c.$$;if(null!==b.fragment){b.update();b.before_update.forEach(ia);let d=b.dirty;b.dirty=[-1];b.fragment&&b.fragment.p(b.ctx,d);b.after_update.forEach(La)}}}catch(c){throw la=
ma.length=0,c;}P=null;for(la=ma.length=0;na.length;)na.pop()();for(let c=0;c<ka.length;c+=1){let d=ka[c];Ma.has(d)||(Ma.add(d),d())}ka.length=0}while(ma.length);for(;mb.length;)mb.pop()();Na=!1;Ma.clear();P=a}}function Xb(a){let b=[],c=[];ka.forEach(d=>-1===a.indexOf(d)?b.push(d):c.push(d));c.forEach(d=>d());ka=b}function aa(){ba={r:0,c:[],p:ba}}function ca(){ba.r||ba.c.forEach(ia);ba=ba.p}function y(a,b){a&&a.i&&(Ba.delete(a),a.i(b))}function C(a,b,c,d){a&&a.o?Ba.has(a)||(Ba.add(a),ba.c.push(()=>
{Ba.delete(a);d&&(c&&a.d(1),d())}),a.o(b)):d&&d()}function da(a){a&&a.c()}function V(a,b,c,d){let {fragment:e,after_update:f}=a.$$;e&&e.m(b,c);d||La(()=>{let g=a.$$.on_mount.map(ia).filter(Ka);a.$$.on_destroy?a.$$.on_destroy.push(...g):g.forEach(ia);a.$$.on_mount=[]});f.forEach(La)}function W(a,b){a=a.$$;null!==a.fragment&&(Xb(a.after_update),a.on_destroy.forEach(ia),a.fragment&&a.fragment.d(b),a.on_destroy=a.fragment=null,a.ctx=[])}function Q(a,b,c,d,e,f,g,h){void 0===h&&(h=[-1]);let k=P;P=a;let l=
a.$$={fragment:null,ctx:[],props:f,update:H,not_equal:e,bound:Object.create(null),on_mount:[],on_destroy:[],on_disconnect:[],before_update:[],after_update:[],context:new Map(b.context||(k?k.$$.context:[])),callbacks:Object.create(null),dirty:h,skip_bound:!1,root:b.target||k.$$.root};g&&g(l.root);let p=!1;l.ctx=c?c(a,b.props||{},function(q,n){let t=(2>=arguments.length?0:arguments.length-2)?2>=arguments.length?void 0:arguments[2]:n;if(l.ctx&&e(l.ctx[q],l.ctx[q]=t)){if(!l.skip_bound&&l.bound[q])l.bound[q](t);
p&&(-1===a.$$.dirty[0]&&(ma.push(a),Na||(Na=!0,Yb.then(lb)),a.$$.dirty.fill(0)),a.$$.dirty[q/31|0]|=1<<q%31)}return n}):[];l.update();p=!0;l.before_update.forEach(ia);l.fragment=d?d(l.ctx):!1;b.target&&(b.hydrate?(c=Array.from(b.target.childNodes),l.fragment&&l.fragment.l(c),c.forEach(I)):l.fragment&&l.fragment.c(),b.intro&&y(a.$$.fragment),V(a,b.target,b.anchor,b.customElement),lb());P=k}function Zb(a){let b,c,d,e,f;return{c(){b=document.createElement("button");A(b,"aria-label",c=a[3]?a[3]:null);
A(b,"class",d=`${a[1]||""} shepherd-button ${a[4]?"shepherd-button-secondary":""}`);b.disabled=a[2];A(b,"tabindex","0")},m(g,h){g.insertBefore(b,h||null);b.innerHTML=a[5];e||(f=za(b,"click",function(){Ka(a[0])&&a[0].apply(this,arguments)}),e=!0)},p(g,h){[h]=h;a=g;h&32&&(b.innerHTML=a[5]);h&8&&c!==(c=a[3]?a[3]:null)&&A(b,"aria-label",c);h&18&&d!==(d=`${a[1]||""} shepherd-button ${a[4]?"shepherd-button-secondary":""}`)&&A(b,"class",d);h&4&&(b.disabled=a[2])},i:H,o:H,d(g){g&&I(b);e=!1;f()}}}function $b(a,
b,c){function d(n){return X(n)?n.call(f):n}let {config:e,step:f}=b,g,h,k,l,p,q;a.$$set=n=>{"config"in n&&c(6,e=n.config);"step"in n&&c(7,f=n.step)};a.$$.update=()=>{a.$$.dirty&192&&(c(0,g=e.action?e.action.bind(f.tour):null),c(1,h=e.classes),c(2,k=e.disabled?d(e.disabled):!1),c(3,l=e.label?d(e.label):null),c(4,p=e.secondary),c(5,q=e.text?d(e.text):null))};return[g,h,k,l,p,q,e,f]}function nb(a,b,c){a=a.slice();a[2]=b[c];return a}function ob(a){let b,c,d=a[1],e=[];for(let g=0;g<d.length;g+=1)e[g]=pb(nb(a,
d,g));let f=g=>C(e[g],1,1,()=>{e[g]=null});return{c(){for(let g=0;g<e.length;g+=1)e[g].c();b=document.createTextNode("")},m(g,h){for(let k=0;k<e.length;k+=1)e[k]&&e[k].m(g,h);g.insertBefore(b,h||null);c=!0},p(g,h){if(h&3){d=g[1];let k;for(k=0;k<d.length;k+=1){let l=nb(g,d,k);e[k]?(e[k].p(l,h),y(e[k],1)):(e[k]=pb(l),e[k].c(),y(e[k],1),e[k].m(b.parentNode,b))}aa();for(k=d.length;k<e.length;k+=1)f(k);ca()}},i(g){if(!c){for(g=0;g<d.length;g+=1)y(e[g]);c=!0}},o(g){e=e.filter(Boolean);for(g=0;g<e.length;g+=
1)C(e[g]);c=!1},d(g){var h=e;for(let k=0;k<h.length;k+=1)h[k]&&h[k].d(g);g&&I(b)}}}function pb(a){let b,c;b=new ac({props:{config:a[2],step:a[0]}});return{c(){da(b.$$.fragment)},m(d,e){V(b,d,e);c=!0},p(d,e){let f={};e&2&&(f.config=d[2]);e&1&&(f.step=d[0]);b.$set(f)},i(d){c||(y(b.$$.fragment,d),c=!0)},o(d){C(b.$$.fragment,d);c=!1},d(d){W(b,d)}}}function bc(a){let b,c,d=a[1]&&ob(a);return{c(){b=document.createElement("footer");d&&d.c();A(b,"class","shepherd-footer")},m(e,f){e.insertBefore(b,f||null);
d&&d.m(b,null);c=!0},p(e,f){[f]=f;e[1]?d?(d.p(e,f),f&2&&y(d,1)):(d=ob(e),d.c(),y(d,1),d.m(b,null)):d&&(aa(),C(d,1,1,()=>{d=null}),ca())},i(e){c||(y(d),c=!0)},o(e){C(d);c=!1},d(e){e&&I(b);d&&d.d()}}}function cc(a,b,c){let d,{step:e}=b;a.$$set=f=>{"step"in f&&c(0,e=f.step)};a.$$.update=()=>{a.$$.dirty&1&&c(1,d=e.options.buttons)};return[e,d]}function dc(a){let b,c,d,e,f;return{c(){b=document.createElement("button");c=document.createElement("span");c.textContent="\u00d7";A(c,"aria-hidden","true");A(b,
"aria-label",d=a[0].label?a[0].label:"Close Tour");A(b,"class","shepherd-cancel-icon");A(b,"type","button")},m(g,h){g.insertBefore(b,h||null);b.appendChild(c);e||(f=za(b,"click",a[1]),e=!0)},p(g,h){[h]=h;h&1&&d!==(d=g[0].label?g[0].label:"Close Tour")&&A(b,"aria-label",d)},i:H,o:H,d(g){g&&I(b);e=!1;f()}}}function ec(a,b,c){let {cancelIcon:d,step:e}=b;a.$$set=f=>{"cancelIcon"in f&&c(0,d=f.cancelIcon);"step"in f&&c(2,e=f.step)};return[d,f=>{f.preventDefault();e.cancel()},e]}function fc(a){let b;return{c(){b=
document.createElement("h3");A(b,"id",a[1]);A(b,"class","shepherd-title")},m(c,d){c.insertBefore(b,d||null);a[3](b)},p(c,d){[d]=d;d&2&&A(b,"id",c[1])},i:H,o:H,d(c){c&&I(b);a[3](null)}}}function gc(a,b,c){let {labelId:d,element:e,title:f}=b;Aa().$$.after_update.push(()=>{X(f)&&c(2,f=f());c(0,e.innerHTML=f,e)});a.$$set=g=>{"labelId"in g&&c(1,d=g.labelId);"element"in g&&c(0,e=g.element);"title"in g&&c(2,f=g.title)};return[e,d,f,function(g){na[g?"unshift":"push"](()=>{e=g;c(0,e)})}]}function qb(a){let b,
c;b=new hc({props:{labelId:a[0],title:a[2]}});return{c(){da(b.$$.fragment)},m(d,e){V(b,d,e);c=!0},p(d,e){let f={};e&1&&(f.labelId=d[0]);e&4&&(f.title=d[2]);b.$set(f)},i(d){c||(y(b.$$.fragment,d),c=!0)},o(d){C(b.$$.fragment,d);c=!1},d(d){W(b,d)}}}function rb(a){let b,c;b=new ic({props:{cancelIcon:a[3],step:a[1]}});return{c(){da(b.$$.fragment)},m(d,e){V(b,d,e);c=!0},p(d,e){let f={};e&8&&(f.cancelIcon=d[3]);e&2&&(f.step=d[1]);b.$set(f)},i(d){c||(y(b.$$.fragment,d),c=!0)},o(d){C(b.$$.fragment,d);c=!1},
d(d){W(b,d)}}}function jc(a){let b,c,d,e=a[2]&&qb(a),f=a[3]&&a[3].enabled&&rb(a);return{c(){b=document.createElement("header");e&&e.c();c=document.createTextNode(" ");f&&f.c();A(b,"class","shepherd-header")},m(g,h){g.insertBefore(b,h||null);e&&e.m(b,null);b.appendChild(c);f&&f.m(b,null);d=!0},p(g,h){[h]=h;g[2]?e?(e.p(g,h),h&4&&y(e,1)):(e=qb(g),e.c(),y(e,1),e.m(b,c)):e&&(aa(),C(e,1,1,()=>{e=null}),ca());g[3]&&g[3].enabled?f?(f.p(g,h),h&8&&y(f,1)):(f=rb(g),f.c(),y(f,1),f.m(b,null)):f&&(aa(),C(f,1,1,
()=>{f=null}),ca())},i(g){d||(y(e),y(f),d=!0)},o(g){C(e);C(f);d=!1},d(g){g&&I(b);e&&e.d();f&&f.d()}}}function kc(a,b,c){let {labelId:d,step:e}=b,f,g;a.$$set=h=>{"labelId"in h&&c(0,d=h.labelId);"step"in h&&c(1,e=h.step)};a.$$.update=()=>{a.$$.dirty&2&&(c(2,f=e.options.title),c(3,g=e.options.cancelIcon))};return[d,e,f,g]}function lc(a){let b;return{c(){b=document.createElement("div");A(b,"class","shepherd-text");A(b,"id",a[1])},m(c,d){c.insertBefore(b,d||null);a[3](b)},p(c,d){[d]=d;d&2&&A(b,"id",c[1])},
i:H,o:H,d(c){c&&I(b);a[3](null)}}}function mc(a,b,c){let {descriptionId:d,element:e,step:f}=b;Aa().$$.after_update.push(()=>{let {text:g}=f.options;X(g)&&(g=g.call(f));g instanceof HTMLElement?e.appendChild(g):c(0,e.innerHTML=g,e)});a.$$set=g=>{"descriptionId"in g&&c(1,d=g.descriptionId);"element"in g&&c(0,e=g.element);"step"in g&&c(2,f=g.step)};return[e,d,f,function(g){na[g?"unshift":"push"](()=>{e=g;c(0,e)})}]}function sb(a){let b,c;b=new nc({props:{labelId:a[1],step:a[2]}});return{c(){da(b.$$.fragment)},
m(d,e){V(b,d,e);c=!0},p(d,e){let f={};e&2&&(f.labelId=d[1]);e&4&&(f.step=d[2]);b.$set(f)},i(d){c||(y(b.$$.fragment,d),c=!0)},o(d){C(b.$$.fragment,d);c=!1},d(d){W(b,d)}}}function tb(a){let b,c;b=new oc({props:{descriptionId:a[0],step:a[2]}});return{c(){da(b.$$.fragment)},m(d,e){V(b,d,e);c=!0},p(d,e){let f={};e&1&&(f.descriptionId=d[0]);e&4&&(f.step=d[2]);b.$set(f)},i(d){c||(y(b.$$.fragment,d),c=!0)},o(d){C(b.$$.fragment,d);c=!1},d(d){W(b,d)}}}function ub(a){let b,c;b=new pc({props:{step:a[2]}});return{c(){da(b.$$.fragment)},
m(d,e){V(b,d,e);c=!0},p(d,e){let f={};e&4&&(f.step=d[2]);b.$set(f)},i(d){c||(y(b.$$.fragment,d),c=!0)},o(d){C(b.$$.fragment,d);c=!1},d(d){W(b,d)}}}function qc(a){let b,c=void 0!==a[2].options.title||a[2].options.cancelIcon&&a[2].options.cancelIcon.enabled,d,e=void 0!==a[2].options.text,f,g=Array.isArray(a[2].options.buttons)&&a[2].options.buttons.length,h,k=c&&sb(a),l=e&&tb(a),p=g&&ub(a);return{c(){b=document.createElement("div");k&&k.c();d=document.createTextNode(" ");l&&l.c();f=document.createTextNode(" ");
p&&p.c();A(b,"class","shepherd-content")},m(q,n){q.insertBefore(b,n||null);k&&k.m(b,null);b.appendChild(d);l&&l.m(b,null);b.appendChild(f);p&&p.m(b,null);h=!0},p(q,n){[n]=n;n&4&&(c=void 0!==q[2].options.title||q[2].options.cancelIcon&&q[2].options.cancelIcon.enabled);c?k?(k.p(q,n),n&4&&y(k,1)):(k=sb(q),k.c(),y(k,1),k.m(b,d)):k&&(aa(),C(k,1,1,()=>{k=null}),ca());n&4&&(e=void 0!==q[2].options.text);e?l?(l.p(q,n),n&4&&y(l,1)):(l=tb(q),l.c(),y(l,1),l.m(b,f)):l&&(aa(),C(l,1,1,()=>{l=null}),ca());n&4&&
(g=Array.isArray(q[2].options.buttons)&&q[2].options.buttons.length);g?p?(p.p(q,n),n&4&&y(p,1)):(p=ub(q),p.c(),y(p,1),p.m(b,null)):p&&(aa(),C(p,1,1,()=>{p=null}),ca())},i(q){h||(y(k),y(l),y(p),h=!0)},o(q){C(k);C(l);C(p);h=!1},d(q){q&&I(b);k&&k.d();l&&l.d();p&&p.d()}}}function rc(a,b,c){let {descriptionId:d,labelId:e,step:f}=b;a.$$set=g=>{"descriptionId"in g&&c(0,d=g.descriptionId);"labelId"in g&&c(1,e=g.labelId);"step"in g&&c(2,f=g.step)};return[d,e,f]}function vb(a){let b;return{c(){b=document.createElement("div");
A(b,"class","shepherd-arrow");A(b,"data-popper-arrow","")},m(c,d){c.insertBefore(b,d||null)},d(c){c&&I(b)}}}function sc(a){let b,c,d,e,f,g,h,k,l=a[4].options.arrow&&a[4].options.attachTo&&a[4].options.attachTo.element&&a[4].options.attachTo.on&&vb();d=new tc({props:{descriptionId:a[2],labelId:a[3],step:a[4]}});let p=[{"aria-describedby":e=void 0!==a[4].options.text?a[2]:null},{"aria-labelledby":f=a[4].options.title?a[3]:null},a[1],{role:"dialog"},{tabindex:"0"}],q={};for(let n=0;n<p.length;n+=1)q=
Wb(q,p[n]);return{c(){b=document.createElement("div");l&&l.c();c=document.createTextNode(" ");da(d.$$.fragment);kb(b,q);ja(b,"shepherd-has-cancel-icon",a[5]);ja(b,"shepherd-has-title",a[6]);ja(b,"shepherd-element",!0)},m(n,t){n.insertBefore(b,t||null);l&&l.m(b,null);b.appendChild(c);V(d,b,null);a[13](b);g=!0;h||(k=za(b,"keydown",a[7]),h=!0)},p(n,t){var [r]=t;n[4].options.arrow&&n[4].options.attachTo&&n[4].options.attachTo.element&&n[4].options.attachTo.on?l||(l=vb(),l.c(),l.m(b,c)):l&&(l.d(1),l=null);
t={};r&4&&(t.descriptionId=n[2]);r&8&&(t.labelId=n[3]);r&16&&(t.step=n[4]);d.$set(t);t=b;r=[(!g||r&20&&e!==(e=void 0!==n[4].options.text?n[2]:null))&&{"aria-describedby":e},(!g||r&24&&f!==(f=n[4].options.title?n[3]:null))&&{"aria-labelledby":f},r&2&&n[1],{role:"dialog"},{tabindex:"0"}];let m={},z={},w={$$scope:1},x=p.length;for(;x--;){let v=p[x],u=r[x];if(u){for(let B in v)B in u||(z[B]=1);for(let B in u)w[B]||(m[B]=u[B],w[B]=1);p[x]=u}else for(let B in v)w[B]=1}for(let v in z)v in m||(m[v]=void 0);
kb(t,q=m);ja(b,"shepherd-has-cancel-icon",n[5]);ja(b,"shepherd-has-title",n[6]);ja(b,"shepherd-element",!0)},i(n){g||(y(d.$$.fragment,n),g=!0)},o(n){C(d.$$.fragment,n);g=!1},d(n){n&&I(b);l&&l.d();W(d);a[13](null);h=!1;k()}}}function wb(a){return a.split(" ").filter(b=>!!b.length)}function uc(a,b,c){let {classPrefix:d,element:e,descriptionId:f,firstFocusableElement:g,focusableElements:h,labelId:k,lastFocusableElement:l,step:p,dataStepId:q}=b,n,t,r;Aa().$$.on_mount.push(()=>{c(1,q={[`data-${d}shepherd-step-id`]:p.id});
c(9,h=e.querySelectorAll('a[href], area[href], input:not([disabled]), select:not([disabled]), textarea:not([disabled]), button:not([disabled]), [tabindex="0"]'));c(8,g=h[0]);c(10,l=h[h.length-1])});Aa().$$.after_update.push(()=>{if(r!==p.options.classes){var m=r;pa(m)&&(m=wb(m),m.length&&e.classList.remove(...m));m=r=p.options.classes;pa(m)&&(m=wb(m),m.length&&e.classList.add(...m))}});a.$$set=m=>{"classPrefix"in m&&c(11,d=m.classPrefix);"element"in m&&c(0,e=m.element);"descriptionId"in m&&c(2,f=
m.descriptionId);"firstFocusableElement"in m&&c(8,g=m.firstFocusableElement);"focusableElements"in m&&c(9,h=m.focusableElements);"labelId"in m&&c(3,k=m.labelId);"lastFocusableElement"in m&&c(10,l=m.lastFocusableElement);"step"in m&&c(4,p=m.step);"dataStepId"in m&&c(1,q=m.dataStepId)};a.$$.update=()=>{a.$$.dirty&16&&(c(5,n=p.options&&p.options.cancelIcon&&p.options.cancelIcon.enabled),c(6,t=p.options&&p.options.title))};return[e,q,f,k,p,n,t,m=>{const {tour:z}=p;switch(m.keyCode){case 9:if(0===h.length){m.preventDefault();
break}if(m.shiftKey){if(document.activeElement===g||document.activeElement.classList.contains("shepherd-element"))m.preventDefault(),l.focus()}else document.activeElement===l&&(m.preventDefault(),g.focus());break;case 27:z.options.exitOnEsc&&p.cancel();break;case 37:z.options.keyboardNavigation&&z.back();break;case 39:z.options.keyboardNavigation&&z.next()}},g,h,l,d,()=>e,function(m){na[m?"unshift":"push"](()=>{e=m;c(0,e)})}]}function vc(a){a&&({steps:a}=a,a.forEach(b=>{b.options&&!1===b.options.canClickTarget&&
b.options.attachTo&&b.target instanceof HTMLElement&&b.target.classList.remove("shepherd-target-click-disabled")}))}function wc(a){let b,c,d,e,f;return{c(){b=jb("svg");c=jb("path");A(c,"d",a[2]);A(b,"class",d=`${a[1]?"shepherd-modal-is-visible":""} shepherd-modal-overlay-container`)},m(g,h){g.insertBefore(b,h||null);b.appendChild(c);a[11](b);e||(f=za(b,"touchmove",a[3]),e=!0)},p(g,h){[h]=h;h&4&&A(c,"d",g[2]);h&2&&d!==(d=`${g[1]?"shepherd-modal-is-visible":""} shepherd-modal-overlay-container`)&&A(b,
"class",d)},i:H,o:H,d(g){g&&I(b);a[11](null);e=!1;f()}}}function xb(a){if(!a)return null;let b=a instanceof HTMLElement&&window.getComputedStyle(a).overflowY;return"hidden"!==b&&"visible"!==b&&a.scrollHeight>=a.clientHeight?a:xb(a.parentElement)}function xc(a,b,c){function d(){c(4,p={width:0,height:0,x:0,y:0,r:0})}function e(){c(1,q=!1);h()}function f(m,z,w,x){void 0===m&&(m=0);void 0===z&&(z=0);if(x){var v=x.getBoundingClientRect();let B=v.y||v.top;v=v.bottom||B+v.height;if(w){var u=w.getBoundingClientRect();
w=u.y||u.top;u=u.bottom||w+u.height;B=Math.max(B,w);v=Math.min(v,u)}let {y:M,height:D}={y:B,height:Math.max(v-B,0)},{x:E,width:R,left:yc}=x.getBoundingClientRect();c(4,p={width:R+2*m,height:D+2*m,x:(E||yc)-m,y:M-m,r:z})}else d()}function g(){c(1,q=!0)}function h(){n&&(cancelAnimationFrame(n),n=void 0);window.removeEventListener("touchmove",r,{passive:!1})}function k(m){let {modalOverlayOpeningPadding:z,modalOverlayOpeningRadius:w}=m.options,x=xb(m.target),v=()=>{n=void 0;f(z,w,x,m.target);n=requestAnimationFrame(v)};
v();window.addEventListener("touchmove",r,{passive:!1})}let {element:l,openingProperties:p}=b;Ca();let q=!1,n=void 0,t;d();let r=m=>{m.preventDefault()};a.$$set=m=>{"element"in m&&c(0,l=m.element);"openingProperties"in m&&c(4,p=m.openingProperties)};a.$$.update=()=>{if(a.$$.dirty&16){let {width:m,height:z,x:w=0,y:x=0,r:v=0}=p,{innerWidth:u,innerHeight:B}=window,{topLeft:M=0,topRight:D=0,bottomRight:E=0,bottomLeft:R=0}="number"===typeof v?{topLeft:v,topRight:v,bottomRight:v,bottomLeft:v}:v;c(2,t=`M${u},${B}\
H0\
V0\
H${u}\
V${B}\
Z\
M${w+M},${x}\
a${M},${M},0,0,0-${M},${M}\
V${z+x-R}\
a${R},${R},0,0,0,${R},${R}\
H${m+w-E}\
a${E},${E},0,0,0,${E}-${E}\
V${x+D}\
a${D},${D},0,0,0-${D}-${D}\
Z`)}};return[l,q,t,m=>{m.stopPropagation()},p,()=>l,d,e,f,function(m){h();m.tour.options.useModalOverlay?(k(m),g()):e()},g,function(m){na[m?"unshift":"push"](()=>{l=m;c(0,l)})}]}var Cb=function(a){var b;if(b=!!a&&"object"===typeof a)b=Object.prototype.toString.call(a),b=!("[object RegExp]"===b||"[object Date]"===b||a.$$typeof===zc);return b},zc="function"===typeof Symbol&&Symbol.for?Symbol.for("react.element"):60103;fa.all=function(a,b){if(!Array.isArray(a))throw Error("first argument should be an array");
return a.reduce(function(c,d){return fa(c,d,b)},{})};var Ja=fa;class Oa{on(a,b,c,d){void 0===d&&(d=!1);void 0===this.bindings&&(this.bindings={});void 0===this.bindings[a]&&(this.bindings[a]=[]);this.bindings[a].push({handler:b,ctx:c,once:d});return this}once(a,b,c){return this.on(a,b,c,!0)}off(a,b){if(void 0===this.bindings||void 0===this.bindings[a])return this;void 0===b?delete this.bindings[a]:this.bindings[a].forEach((c,d)=>{c.handler===b&&this.bindings[a].splice(d,1)});return this}trigger(a){for(var b=
arguments.length,c=Array(1<b?b-1:0),d=1;d<b;d++)c[d-1]=arguments[d];void 0!==this.bindings&&this.bindings[a]&&this.bindings[a].forEach((e,f)=>{let {ctx:g,handler:h,once:k}=e;h.apply(g||this,c);k&&this.bindings[a].splice(f,1)});return this}}let Ac="mainAxis crossAxis fallbackPlacements fallbackStrategy fallbackAxisSideDirection flipAlignment".split(" "),Bc=["mainAxis","crossAxis","limiter"],Cc=async(a,b,c)=>{const {placement:d="bottom",strategy:e="absolute",middleware:f=[],platform:g}=c;c=f.filter(Boolean);
const h=await (null==g.isRTL?void 0:g.isRTL(b));let k=await g.getElementRects({reference:a,floating:b,strategy:e}),{x:l,y:p}=Xa(k,d,h),q=d,n={},t=0;for(let r=0;r<c.length;r++){const {name:m,fn:z}=c[r],{x:w,y:x,data:v,reset:u}=await z({x:l,y:p,initialPlacement:d,placement:q,strategy:e,middlewareData:n,rects:k,platform:g,elements:{reference:a,floating:b}});l=null!=w?w:l;p=null!=x?x:p;n=F({},n,{[m]:F({},n[m],v)});u&&50>=t&&(t++,"object"===typeof u&&(u.placement&&(q=u.placement),u.rects&&(k=!0===u.rects?
await g.getElementRects({reference:a,floating:b,strategy:e}):u.rects),{x:l,y:p}=Xa(k,q,h)),r=-1)}return{x:l,y:p,placement:q,strategy:e,middlewareData:n}},Pa=Math.min,Qa=Math.max,Vb=a=>({name:"arrow",options:a,async fn(b){const {element:c,padding:d=0}=a||{},{x:e,y:f,placement:g,rects:h,platform:k,elements:l}=b;if(null==c)return{};var p=Ya(d);b={x:e,y:f};const q=qa(g),n=Da(q),t=await k.getDimensions(c);var r="y"===q;const m=r?"top":"left",z=r?"bottom":"right";r=r?"clientHeight":"clientWidth";var w=
h.reference[n]+h.reference[q]-b[q]-h.floating[n],x=b[q]-h.reference[q],v=await (null==k.getOffsetParent?void 0:k.getOffsetParent(c));let u=v?v[r]:0;u&&await (null==k.isElement?void 0:k.isElement(v))||(u=l.floating[r]||h.floating[n]);r=p[m];v=u-t[n]-p[z];w=u/2-t[n]/2+(w/2-x/2);x=Qa(r,Pa(w,v));p=null!=g.split("-")[1]&&w!=x&&0>h.reference[n]/2-(w<r?p[m]:p[z])-t[n]/2;return{[q]:b[q]-(p?w<r?r-w:v-w:0),data:{[q]:x,centerOffset:w-x}}}}),Fb={left:"right",right:"left",bottom:"top",top:"bottom"},Hb={start:"end",
end:"start"},Sb=function(a){void 0===a&&(a={});return{name:"flip",options:a,async fn(b){var c;const {placement:d,middlewareData:e,rects:f,initialPlacement:g,platform:h,elements:k}=b,{mainAxis:l=!0,crossAxis:p=!0,fallbackPlacements:q,fallbackStrategy:n="bestFit",fallbackAxisSideDirection:t="none",flipAlignment:r=!0}=a;var m=Wa(a,Ac);const z=Y(d);var w=Y(g)===g;const x=await (null==h.isRTL?void 0:h.isRTL(k.floating));w=q||(w||!r?[va(g)]:Ib(g));q||"none"===t||w.push(...Kb(g,r,t,x));w=[g,...w];m=await Za(b,
m);const v=[];b=(null==(c=e.flip)?void 0:c.overflows)||[];l&&v.push(m[z]);if(p){const {main:D,cross:E}=Gb(d,f,x);v.push(m[D],m[E])}b=[...b,{placement:d,overflows:v}];if(!v.every(D=>0>=D)){var u,B;c=((null==(u=e.flip)?void 0:u.index)||0)+1;if(u=w[c])return{data:{index:c,overflows:b},reset:{placement:u}};u=null==(B=b.filter(D=>0>=D.overflows[0]).sort((D,E)=>D.overflows[1]-E.overflows[1])[0])?void 0:B.placement;if(!u)switch(n){case "bestFit":var M;(B=null==(M=b.map(D=>[D.placement,D.overflows.filter(E=>
0<E).reduce((E,R)=>E+R,0)]).sort((D,E)=>D[1]-E[1])[0])?void 0:M[0])&&(u=B);break;case "initialPlacement":u=g}if(d!==u)return{reset:{placement:u}}}return{}}}},Tb=function(a){void 0===a&&(a={});return{name:"shift",options:a,async fn(b){const {x:c,y:d,placement:e}=b,{mainAxis:f=!0,crossAxis:g=!1,limiter:h={fn:t=>{let {x:r,y:m}=t;return{x:r,y:m}}}}=a;var k=Wa(a,Bc),l={x:c,y:d};k=await Za(b,k);const p=qa(Y(e)),q="x"===p?"y":"x";let n=l[p];l=l[q];f&&(n=Qa(n+k["y"===p?"top":"left"],Pa(n,n-k["y"===p?"bottom":
"right"])));g&&(l=Qa(l+k["y"===q?"top":"left"],Pa(l,l-k["y"===q?"bottom":"right"])));b=h.fn(F({},b,{[p]:n,[q]:l}));return F({},b,{data:{x:b.x-c,y:b.y-d}})}}},Ub=function(a){void 0===a&&(a={});return{options:a,fn(b){const {x:c,y:d,placement:e,rects:f,middlewareData:g}=b,{offset:h=0,mainAxis:k=!0,crossAxis:l=!0}=a;var p={x:c,y:d};const q=qa(e),n="x"===q?"y":"x";let t=p[q];p=p[n];b="function"===typeof h?h(b):h;b="number"===typeof b?{mainAxis:b,crossAxis:0}:F({mainAxis:0,crossAxis:0},b);if(k){var r="y"===
q?"height":"width",m=f.reference[q]-f.floating[r]+b.mainAxis;r=f.reference[q]+f.reference[r]-b.mainAxis;t<m?t=m:t>r&&(t=r)}if(l){var z,w;r="y"===q?"width":"height";const x=["top","left"].includes(Y(e));m=f.reference[n]-f.floating[r]+(x?(null==(z=g.offset)?void 0:z[n])||0:0)+(x?0:b.crossAxis);z=f.reference[n]+f.reference[r]+(x?0:(null==(w=g.offset)?void 0:w[n])||0)-(x?b.crossAxis:0);p<m?p=m:p>z&&(p=z)}return{[q]:t,[n]:p}}}},Fa,yb=Math.min,ta=Math.max,xa=Math.round,db={x:1,y:1},Dc={getClippingRect:function(a){let {element:b,
boundary:c,rootBoundary:d,strategy:e}=a;a=[...("clippingAncestors"===c?Lb(b,this._c):[].concat(c)),d];a=a.reduce((f,g)=>{g=gb(b,g,e);f.top=ta(g.top,f.top);f.right=yb(g.right,f.right);f.bottom=yb(g.bottom,f.bottom);f.left=ta(g.left,f.left);return f},gb(b,a[0],e));return{width:a.right-a.left,height:a.bottom-a.top,x:a.left,y:a.top}},convertOffsetParentRelativeRectToViewportRelativeRect:function(a){let {rect:b,offsetParent:c,strategy:d}=a;var e=J(c);let f=U(c);if(c===f)return b;a={scrollLeft:0,scrollTop:0};
let g={x:1,y:1};var h=0,k=0;if(e||!e&&"fixed"!==d){if("body"!==T(c)||wa(f))a=ya(c);J(c)&&(e=Z(c),g=ha(c),h=e.x+c.clientLeft,k=e.y+c.clientTop)}return{width:b.width*g.x,height:b.height*g.y,x:b.x*g.x-a.scrollLeft*g.x+h,y:b.y*g.y-a.scrollTop*g.y+k}},isElement:K,getDimensions:function(a){return bb(a)},getOffsetParent:ib,getDocumentElement:U,getScale:ha,async getElementRects(a){let {reference:b,floating:c,strategy:d}=a;a=this.getDimensions;var e=await (this.getOffsetParent||ib)(c),f=J(e);let g=U(e),h=
Z(b,!0,"fixed"===d,e),k={scrollLeft:0,scrollTop:0};var l=0,p=0;if(f||!f&&"fixed"!==d){if("body"!==T(e)||wa(g))k=ya(e);J(e)?(f=Z(e,!0),l=f.x+e.clientLeft,p=f.y+e.clientTop):g&&(l=eb(g))}return{reference:{x:h.left+k.scrollLeft-l,y:h.top+k.scrollTop-p,width:h.width,height:h.height},floating:F({x:0,y:0},await a(c))}},getClientRects:a=>Array.from(a.getClientRects()),isRTL:a=>"rtl"===L(a).direction},Qb=(a,b,c)=>{var d=new Map;c=F({platform:Dc},c);d=F({},c.platform,{_c:d});return Cc(a,b,F({},c,{platform:d}))},
P,ma=[],na=[],ka=[],mb=[],Yb=Promise.resolve(),Na=!1,Ma=new Set,la=0,Ba=new Set,ba;class S{$destroy(){W(this,1);this.$destroy=H}$on(a,b){if(!Ka(b))return H;let c=this.$$.callbacks[a]||(this.$$.callbacks[a]=[]);c.push(b);return()=>{let d=c.indexOf(b);-1!==d&&c.splice(d,1)}}$set(a){this.$$set&&0!==Object.keys(a).length&&(this.$$.skip_bound=!0,this.$$set(a),this.$$.skip_bound=!1)}}class ac extends S{constructor(a){super();Q(this,a,$b,Zb,O,{config:6,step:7})}}class pc extends S{constructor(a){super();
Q(this,a,cc,bc,O,{step:0})}}class ic extends S{constructor(a){super();Q(this,a,ec,dc,O,{cancelIcon:0,step:2})}}class hc extends S{constructor(a){super();Q(this,a,gc,fc,O,{labelId:1,element:0,title:2})}}class nc extends S{constructor(a){super();Q(this,a,kc,jc,O,{labelId:0,step:1})}}class oc extends S{constructor(a){super();Q(this,a,mc,lc,O,{descriptionId:1,element:0,step:2})}}class tc extends S{constructor(a){super();Q(this,a,rc,qc,O,{descriptionId:0,labelId:1,step:2})}}class Ec extends S{constructor(a){super();
Q(this,a,uc,sc,O,{classPrefix:11,element:0,descriptionId:2,firstFocusableElement:8,focusableElements:9,labelId:3,lastFocusableElement:10,step:4,dataStepId:1,getElement:12})}get getElement(){return this.$$.ctx[12]}}class Ra extends Oa{constructor(a,b){void 0===b&&(b={});super(a,b);this.tour=a;this.classPrefix=this.tour.options?Va(this.tour.options.classPrefix):"";this.styles=a.styles;this._resolvedAttachTo=null;Ua(this);this._setOptions(b);return this}cancel(){this.tour.cancel();this.trigger("cancel")}complete(){this.tour.complete();
this.trigger("complete")}destroy(){this.cleanup&&this.cleanup();this.cleanup=null;this.el instanceof HTMLElement&&(this.el.remove(),this.el=null);this._updateStepTargetOnHide();this.trigger("destroy")}getTour(){return this.tour}hide(){this.tour.modal.hide();this.trigger("before-hide");this.el&&(this.el.hidden=!0);this._updateStepTargetOnHide();this.trigger("hide")}_resolveAttachToOptions(){let a=this.options.attachTo||{},b=Object.assign({},a);X(b.element)&&(b.element=b.element.call(this));if(pa(b.element)){try{b.element=
document.querySelector(b.element)}catch(c){}b.element||console.error(`The element for this Shepherd step was not found ${a.element}`)}return this._resolvedAttachTo=b}_getResolvedAttachToOptions(){return null===this._resolvedAttachTo?this._resolveAttachToOptions():this._resolvedAttachTo}isOpen(){return!(!this.el||this.el.hidden)}show(){return X(this.options.beforeShowPromise)?Promise.resolve(this.options.beforeShowPromise()).then(()=>this._show()):Promise.resolve(this._show())}updateStepOptions(a){Object.assign(this.options,
a);this.shepherdElementComponent&&this.shepherdElementComponent.$set({step:this})}getElement(){return this.el}getTarget(){return this.target}_createTooltipContent(){this.shepherdElementComponent=new Ec({target:this.tour.options.stepsContainer||document.body,props:{classPrefix:this.classPrefix,descriptionId:`${this.id}-description`,labelId:`${this.id}-label`,step:this,styles:this.styles}});return this.shepherdElementComponent.getElement()}_scrollTo(a){let {element:b}=this._getResolvedAttachToOptions();
X(this.options.scrollToHandler)?this.options.scrollToHandler(b):b instanceof Element&&"function"===typeof b.scrollIntoView&&b.scrollIntoView(a)}_getClassOptions(a){var b=this.tour&&this.tour.options&&this.tour.options.defaultStepOptions;b=b&&b.classes?b.classes:"";a=[...(a.classes?a.classes:"").split(" "),...b.split(" ")];a=new Set(a);return Array.from(a).join(" ").trim()}_setOptions(a){void 0===a&&(a={});let b=this.tour&&this.tour.options&&this.tour.options.defaultStepOptions;b=Ja({},b||{});this.options=
Object.assign({arrow:!0},b,a,{floatingUIOptions:Ja(b.floatingUIOptions||{},a.floatingUIOptions||{})});let {when:c}=this.options;this.options.classes=this._getClassOptions(a);this.destroy();this.id=this.options.id||`step-${Ca()}`;c&&Object.keys(c).forEach(d=>{this.on(d,c[d],this)})}_setupElements(){void 0!==this.el&&this.destroy();this.el=this._createTooltipContent();this.options.advanceOn&&Eb(this);Nb(this)}_show(){this.trigger("before-show");this._resolveAttachToOptions();this._setupElements();this.tour.modal||
this.tour._setupModal();this.tour.modal.setupForStep(this);this._styleTargetElementForStep(this);this.el.hidden=!1;this.options.scrollTo&&setTimeout(()=>{this._scrollTo(this.options.scrollTo)});this.el.hidden=!1;let a=this.shepherdElementComponent.getElement(),b=this.target||document.body;b.classList.add(`${this.classPrefix}shepherd-enabled`);b.classList.add(`${this.classPrefix}shepherd-target`);a.classList.add("shepherd-enabled");this.trigger("show")}_styleTargetElementForStep(a){let b=a.target;
b&&(a.options.highlightClass&&b.classList.add(a.options.highlightClass),b.classList.remove("shepherd-target-click-disabled"),!1===a.options.canClickTarget&&b.classList.add("shepherd-target-click-disabled"))}_updateStepTargetOnHide(){let a=this.target||document.body;this.options.highlightClass&&a.classList.remove(this.options.highlightClass);a.classList.remove("shepherd-target-click-disabled",`${this.classPrefix}shepherd-enabled`,`${this.classPrefix}shepherd-target`)}}class Fc extends S{constructor(a){super();
Q(this,a,xc,wc,O,{element:0,openingProperties:4,getElement:5,closeModalOpening:6,hide:7,positionModal:8,setupForStep:9,show:10})}get getElement(){return this.$$.ctx[5]}get closeModalOpening(){return this.$$.ctx[6]}get hide(){return this.$$.ctx[7]}get positionModal(){return this.$$.ctx[8]}get setupForStep(){return this.$$.ctx[9]}get show(){return this.$$.ctx[10]}}let ea=new Oa;class Gc extends Oa{constructor(a){void 0===a&&(a={});super(a);Ua(this);this.options=Object.assign({},{exitOnEsc:!0,keyboardNavigation:!0},
a);this.classPrefix=Va(this.options.classPrefix);this.steps=[];this.addSteps(this.options.steps);"active cancel complete inactive show start".split(" ").map(b=>{(c=>{this.on(c,d=>{d=d||{};d.tour=this;ea.trigger(c,d)})})(b)});this._setTourID();return this}addStep(a,b){a instanceof Ra?a.tour=this:a=new Ra(this,a);void 0!==b?this.steps.splice(b,0,a):this.steps.push(a);return a}addSteps(a){Array.isArray(a)&&a.forEach(b=>{this.addStep(b)});return this}back(){let a=this.steps.indexOf(this.currentStep);
this.show(a-1,!1)}async cancel(){if(this.options.confirmCancel){let a=this.options.confirmCancelMessage||"Are you sure you want to stop the tour?";("function"===typeof this.options.confirmCancel?await this.options.confirmCancel():window.confirm(a))&&this._done("cancel")}else this._done("cancel")}complete(){this._done("complete")}getById(a){return this.steps.find(b=>b.id===a)}getCurrentStep(){return this.currentStep}hide(){let a=this.getCurrentStep();if(a)return a.hide()}isActive(){return ea.activeTour===
this}next(){let a=this.steps.indexOf(this.currentStep);a===this.steps.length-1?this.complete():this.show(a+1,!0)}removeStep(a){let b=this.getCurrentStep();this.steps.some((c,d)=>{if(c.id===a)return c.isOpen()&&c.hide(),c.destroy(),this.steps.splice(d,1),!0});b&&b.id===a&&(this.currentStep=void 0,this.steps.length?this.show(0):this.cancel())}show(a,b){void 0===a&&(a=0);void 0===b&&(b=!0);if(a=pa(a)?this.getById(a):this.steps[a])this._updateStateBeforeShow(),X(a.options.showOn)&&!a.options.showOn()?
this._skipStep(a,b):(this.trigger("show",{step:a,previous:this.currentStep}),this.currentStep=a,a.show())}start(){this.trigger("start");this.focusedElBeforeOpen=document.activeElement;this.currentStep=null;this._setupModal();this._setupActiveTour();this.next()}_done(a){let b=this.steps.indexOf(this.currentStep);Array.isArray(this.steps)&&this.steps.forEach(c=>c.destroy());vc(this);this.trigger(a,{index:b});ea.activeTour=null;this.trigger("inactive",{tour:this});this.modal&&this.modal.hide();"cancel"!==
a&&"complete"!==a||!this.modal||(a=document.querySelector(".shepherd-modal-overlay-container"))&&a.remove();this.focusedElBeforeOpen instanceof HTMLElement&&this.focusedElBeforeOpen.focus()}_setupActiveTour(){this.trigger("active",{tour:this});ea.activeTour=this}_setupModal(){this.modal=new Fc({target:this.options.modalContainer||document.body,props:{classPrefix:this.classPrefix,styles:this.styles}})}_skipStep(a,b){a=this.steps.indexOf(a);a===this.steps.length-1?this.complete():this.show(b?a+1:a-
1,b)}_updateStateBeforeShow(){this.currentStep&&this.currentStep.hide();this.isActive()||this._setupActiveTour()}_setTourID(){this.id=`${this.options.tourName||"tour"}--${Ca()}`}}class zb{constructor(){}}"undefined"===typeof window?Object.assign(ea,{Tour:zb,Step:zb}):Object.assign(ea,{Tour:Gc,Step:Ra});return ea})
//# sourceMappingURL=shepherd.min.js.map
