<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>GMaps.js &mdash; Static map with polyline</title>
  <script src="http://ajax.googleapis.com/ajax/libs/jquery/1.7.1/jquery.min.js"></script>
  <script src="http://maps.google.com/maps/api/js?sensor=true"></script>
  <script src="../gmaps.js"></script>
  <link rel="stylesheet" href="http://twitter.github.com/bootstrap/1.3.0/bootstrap.min.css" />
  <link rel="stylesheet" type="text/css" href="examples.css" />
  <script>
    $(document).ready(function(){
      var path = [
        [-12.040397656836609,-77.03373871559225],
        [-12.040248585302038,-77.03993927003302],
        [-12.050047116528843,-77.02448169303511],
        [-12.044804866577001,-77.02154422636042],
        [-12.040397656836609,-77.03373871559225]
      ];

      var url = GMaps.staticMapURL({
        size: [610, 350],
        lat: -12.043333,
        lng: -77.028333,
        polyline: {
          path: path,
          strokeColor: '#131540',
          strokeOpacity: 0.6,
          strokeWeight: 6
        }
      });
      $('<img/>').attr('src', url).appendTo('#map');
    });
  </script>
</head>
<body>
<h1>GMaps.js &mdash; Static map with polyline</h1>
  <div class="row">
    <div class="span11">
      <div id="map"></div>
    </div>
    <div class="span6">
      <p>A single polyline can be also drawed in a static map:</p>
      <pre>url = GMaps.staticMapURL({
  size: [610, 350],
  lat: -12.043333,
  lng: -77.028333,

  polyline: {
    path: path,
    strokeColor: '#131540',
    strokeOpacity: 0.6,
    strokeWeight: 6
  }
});

$('&lt;img/&gt;').attr('src', url)
  .appendTo('#map');</pre>
    </div>
  </div>
</body>
</html>