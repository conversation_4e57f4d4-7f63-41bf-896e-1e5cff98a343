{"version": 3, "sources": ["../src/js/waves.js"], "names": ["window", "factory", "define", "amd", "Waves", "call", "exports", "module", "global", "this", "isWindow", "obj", "getWindow", "elem", "nodeType", "defaultView", "isObject", "value", "type", "isDOMNode", "getWavesElements", "nodes", "stringRepr", "toString", "$$", "test", "hasOwnProperty", "offset", "doc<PERSON><PERSON>", "win", "box", "top", "left", "doc", "ownerDocument", "documentElement", "getBoundingClientRect", "pageYOffset", "clientTop", "pageXOffset", "clientLeft", "convertStyle", "styleObj", "style", "prop", "removeRipple", "e", "el", "ripple", "classList", "remove", "relativeX", "getAttribute", "relativeY", "scale", "translate", "delay", "Date", "now", "Number", "duration", "Effect", "setTimeout", "opacity", "-webkit-transition-duration", "-moz-transition-duration", "-o-transition-duration", "transition-duration", "-webkit-transform", "-moz-transform", "-ms-transform", "-o-transform", "transform", "setAttribute", "<PERSON><PERSON><PERSON><PERSON>", "getWavesEffectElement", "TouchHandler", "allowEvent", "element", "target", "srcElement", "parentElement", "SVGElement", "contains", "showEffect", "disabled", "registerEvent", "hidden", "timer", "show", "hideEffect", "hideEvent", "clearTimeout", "hide", "removeListeners", "touchMove", "moveEvent", "addEventListener", "removeEventListener", "isTouchAvailable", "document", "querySelectorAll", "bind", "Object", "prototype", "velocity", "button", "createElement", "className", "append<PERSON><PERSON><PERSON>", "pos", "touches", "length", "pageY", "pageX", "clientWidth", "rippleStyle", "add", "ripples", "getElementsByClassName", "i", "len", "TagWrapper", "input", "parent", "parentNode", "tagName", "toLowerCase", "wrapper", "<PERSON><PERSON><PERSON><PERSON>", "elementStyle", "getComputedStyle", "color", "backgroundColor", "img", "allow", "eType", "init", "options", "body", "attach", "elements", "classes", "join", "indexOf", "elementsLen", "wait", "position", "off", "centre", "mousedown", "x", "y", "clientHeight", "mouseup", "hideRipple", "calm", "displayEffect"], "mappings": "CASC,SAAUA,EAAQC,GACf,aAIsB,mBAAXC,QAAyBA,OAAOC,IACvCD,UAAW,WAEP,OADAF,EAAOI,MAAQH,EAAQI,KAAKL,GACrBA,EAAOI,QAMM,iBAAZE,QACZC,OAAOD,QAAUL,EAAQI,KAAKL,GAK9BA,EAAOI,MAAQH,EAAQI,KAAKL,GApBnC,CAsBoB,iBAAXQ,OAAsBA,OAASC,KAAM,WAC3C,aASA,SAASC,EAASC,GACd,OAAe,OAARA,GAAgBA,IAAQA,EAAIX,OAGvC,SAASY,EAAUC,GACf,OAAOH,EAASG,GAAQA,EAAyB,IAAlBA,EAAKC,UAAkBD,EAAKE,YAG/D,SAASC,EAASC,GACd,IAAIC,SAAcD,EAClB,MAAgB,aAATC,GAAgC,WAATA,KAAuBD,EAGzD,SAASE,EAAUR,GACf,OAAOK,EAASL,IAAQA,EAAIG,SAAW,EAG3C,SAASM,EAAiBC,GACtB,IAAIC,EAAaC,EAASlB,KAAKgB,GAE/B,MAAmB,oBAAfC,EACOE,EAAGH,GACHL,EAASK,IAAU,sDAAsDI,KAAKH,IAAeD,EAAMK,eAAe,UAClHL,EACAF,EAAUE,IACTA,MAMhB,SAASM,EAAOd,GACZ,IAAIe,EAASC,EACTC,GAAQC,IAAK,EAAGC,KAAM,GACtBC,EAAMpB,GAAQA,EAAKqB,cAQvB,OANAN,EAAUK,EAAIE,qBAE4B,IAA/BtB,EAAKuB,wBACZN,EAAMjB,EAAKuB,yBAEfP,EAAMjB,EAAUqB,IAEZF,IAAKD,EAAIC,IAAMF,EAAIQ,YAAcT,EAAQU,UACzCN,KAAMF,EAAIE,KAAOH,EAAIU,YAAcX,EAAQY,YAInD,SAASC,EAAaC,GAClB,IAAIC,EAAQ,GAEZ,IAAK,IAAIC,KAAQF,EACTA,EAAShB,eAAekB,KACxBD,GAAUC,EAAO,IAAMF,EAASE,GAAQ,KAIhD,OAAOD,EAgKX,SAASE,EAAaC,EAAGC,EAAIC,GAGzB,GAAKA,EAAL,CAIAA,EAAOC,UAAUC,OAAO,kBAExB,IAAIC,EAAYH,EAAOI,aAAa,UAChCC,EAAYL,EAAOI,aAAa,UAChCE,EAAYN,EAAOI,aAAa,cAChCG,EAAYP,EAAOI,aAAa,kBAIhCI,EAAQ,KADDC,KAAKC,MAAQC,OAAOX,EAAOI,aAAa,eAG/CI,EAAQ,IACRA,EAAQ,GAGG,cAAXV,EAAE5B,OACFsC,EAAQ,KAIZ,IAAII,EAAsB,cAAXd,EAAE5B,KAAuB,KAAO2C,EAAOD,SAEtDE,WAAW,WAEP,IAAInB,GACAZ,IAAKsB,EAAY,KACjBrB,KAAMmB,EAAY,KAClBY,QAAS,IAGTC,8BAA+BJ,EAAW,KAC1CK,2BAA4BL,EAAW,KACvCM,yBAA0BN,EAAW,KACrCO,sBAAuBP,EAAW,KAClCQ,oBAAqBd,EAAQ,IAAMC,EACnCc,iBAAkBf,EAAQ,IAAMC,EAChCe,gBAAiBhB,EAAQ,IAAMC,EAC/BgB,eAAgBjB,EAAQ,IAAMC,EAC9BiB,UAAalB,EAAQ,IAAMC,GAG/BP,EAAOyB,aAAa,QAAShC,EAAaE,IAE1CmB,WAAW,WACP,IACIf,EAAG2B,YAAY1B,GACjB,MAAOF,GACL,OAAO,IAEZc,IAEJJ,IAiDP,SAASmB,EAAsB7B,GAE3B,IAAmC,IAA/B8B,EAAaC,WAAW/B,GACxB,OAAO,KAMX,IAHA,IAAIgC,EAAU,KACVC,EAASjC,EAAEiC,QAAUjC,EAAEkC,WAEpBD,EAAOE,eAAe,CACzB,KAAQF,aAAkBG,aAAgBH,EAAO9B,UAAUkC,SAAS,gBAAiB,CACjFL,EAAUC,EACV,MAEJA,EAASA,EAAOE,cAGpB,OAAOH,EAMX,SAASM,EAAWtC,GAQhB,IAAIgC,EAAUH,EAAsB7B,GAEpC,GAAgB,OAAZgC,EAAkB,CAGlB,GAAIA,EAAQO,UAAYP,EAAQ1B,aAAa,aAAe0B,EAAQ7B,UAAUkC,SAAS,YACnF,OAKJ,GAFAP,EAAaU,cAAcxC,GAEZ,eAAXA,EAAE5B,MAAyB2C,EAAOL,MAAO,CAEzC,IAAI+B,GAAS,EAETC,EAAQ1B,WAAW,WACnB0B,EAAQ,KACR3B,EAAO4B,KAAK3C,EAAGgC,IAChBjB,EAAOL,OAENkC,EAAa,SAASC,GAGlBH,IACAI,aAAaJ,GACbA,EAAQ,KACR3B,EAAO4B,KAAK3C,EAAGgC,IAEdS,IACDA,GAAS,EACT1B,EAAOgC,KAAKF,EAAWb,IAG3BgB,KAGAC,EAAY,SAASC,GACjBR,IACAI,aAAaJ,GACbA,EAAQ,MAEZE,EAAWM,GAEXF,KAGJhB,EAAQmB,iBAAiB,YAAaF,GAAW,GACjDjB,EAAQmB,iBAAiB,WAAYP,GAAY,GACjDZ,EAAQmB,iBAAiB,cAAeP,GAAY,GAEpD,IAAII,EAAkB,WAClBhB,EAAQoB,oBAAoB,YAAaH,GACzCjB,EAAQoB,oBAAoB,WAAYR,GACxCZ,EAAQoB,oBAAoB,cAAeR,SAI/C7B,EAAO4B,KAAK3C,EAAGgC,GAEXqB,IACArB,EAAQmB,iBAAiB,WAAYpC,EAAOgC,MAAM,GAClDf,EAAQmB,iBAAiB,cAAepC,EAAOgC,MAAM,IAGzDf,EAAQmB,iBAAiB,UAAWpC,EAAOgC,MAAM,GACjDf,EAAQmB,iBAAiB,aAAcpC,EAAOgC,MAAM,IA3ahE,IAAIzF,EAAmBA,MACnBoB,EAAmB4E,SAASC,iBAAiBC,KAAKF,UAClD7E,EAAmBgF,OAAOC,UAAUjF,SACpC4E,EAAmB,iBAAkBnG,OAgErC6D,GAGAD,SAAU,IAGVJ,MAAO,IAEPiC,KAAM,SAAS3C,EAAGgC,EAAS2B,GAGvB,GAAiB,IAAb3D,EAAE4D,OACF,OAAO,EAGX5B,EAAUA,GAAWrE,KAGrB,IAAIuC,EAASoD,SAASO,cAAc,OACpC3D,EAAO4D,UAAY,8BACnB9B,EAAQ+B,YAAY7D,GAGpB,IAAI8D,EAAYnF,EAAOmD,GACnBzB,EAAY,EACZF,EAAY,EAEb,YAAaL,GAAKA,EAAEiE,QAAQC,QAC3B3D,EAAeP,EAAEiE,QAAQ,GAAGE,MAAQH,EAAI/E,IACxCoB,EAAeL,EAAEiE,QAAQ,GAAGG,MAAQJ,EAAI9E,OAIxCqB,EAAeP,EAAEmE,MAAQH,EAAI/E,IAC7BoB,EAAeL,EAAEoE,MAAQJ,EAAI9E,MAGjCmB,EAAYA,GAAa,EAAIA,EAAY,EACzCE,EAAYA,GAAa,EAAIA,EAAY,EAEzC,IAAIC,EAAY,SAAawB,EAAQqC,YAAc,IAAO,EAAK,IAC3D5D,EAAY,iBAEZkD,IACAlD,EAAY,aAAgBkD,EAAU,EAAI,OAAUA,EAAU,EAAI,OAItEzD,EAAOyB,aAAa,YAAahB,KAAKC,OACtCV,EAAOyB,aAAa,SAAUtB,GAC9BH,EAAOyB,aAAa,SAAUpB,GAC9BL,EAAOyB,aAAa,aAAcnB,GAClCN,EAAOyB,aAAa,iBAAkBlB,GAGtC,IAAI6D,GACArF,IAAKsB,EAAY,KACjBrB,KAAMmB,EAAY,MAGtBH,EAAOC,UAAUoE,IAAI,sBACrBrE,EAAOyB,aAAa,QAAShC,EAAa2E,IAC1CpE,EAAOC,UAAUC,OAAO,sBAGxBkE,EAAY,qBAAuB9D,EAAQ,IAAMC,EACjD6D,EAAY,kBAAoB9D,EAAQ,IAAMC,EAC9C6D,EAAY,iBAAmB9D,EAAQ,IAAMC,EAC7C6D,EAAY,gBAAkB9D,EAAQ,IAAMC,EAC5C6D,EAAY5C,UAAYlB,EAAQ,IAAMC,EACtC6D,EAAYrD,QAAU,IAEtB,IAAIH,EAAsB,cAAXd,EAAE5B,KAAuB,KAAO2C,EAAOD,SACtDwD,EAAY,+BAAiCxD,EAAW,KACxDwD,EAAY,4BAAiCxD,EAAW,KACxDwD,EAAY,0BAAiCxD,EAAW,KACxDwD,EAAY,uBAAiCxD,EAAW,KAExDZ,EAAOyB,aAAa,QAAShC,EAAa2E,KAG9CvB,KAAM,SAAS/C,EAAGgC,GAKd,IAAK,IAFDwC,GAFJxC,EAAUA,GAAWrE,MAEC8G,uBAAuB,kBAEpCC,EAAI,EAAGC,EAAMH,EAAQN,OAAQQ,EAAIC,EAAKD,IAC3C3E,EAAaC,EAAGgC,EAASwC,EAAQE,IAGjCrB,IACArB,EAAQoB,oBAAoB,WAAYrC,EAAOgC,MAC/Cf,EAAQoB,oBAAoB,cAAerC,EAAOgC,OAGtDf,EAAQoB,oBAAoB,UAAWrC,EAAOgC,MAC9Cf,EAAQoB,oBAAoB,aAAcrC,EAAOgC,QAQrD6B,GAGAC,MAAO,SAAS7C,GAEZ,IAAI8C,EAAS9C,EAAQ+C,WAGrB,GAAqC,MAAjCD,EAAOE,QAAQC,gBAAyBH,EAAO3E,UAAUkC,SAAS,gBAAtE,CAKA,IAAI6C,EAAgB5B,SAASO,cAAc,KAC3CqB,EAAQpB,UAAY9B,EAAQ8B,UAAY,uBACxC9B,EAAQ8B,UAAY,qBAGpBgB,EAAOK,aAAaD,EAASlD,GAC7BkD,EAAQnB,YAAY/B,GAGpB,IAAIoD,EAAkBlI,OAAOmI,iBAAiBrD,EAAS,MACnDsD,EAAkBF,EAAaE,MAC/BC,EAAkBH,EAAaG,gBAEnCL,EAAQvD,aAAa,QAAS,SAAW2D,EAAQ,eAAiBC,GAClEvD,EAAQL,aAAa,QAAS,qCAKlC6D,IAAK,SAASxD,GAEV,IAAI8C,EAAS9C,EAAQ+C,WAGrB,GAAqC,MAAjCD,EAAOE,QAAQC,gBAAyBH,EAAO3E,UAAUkC,SAAS,gBAAtE,CAKA,IAAI6C,EAAW5B,SAASO,cAAc,KACtCiB,EAAOK,aAAaD,EAASlD,GAC7BkD,EAAQnB,YAAY/B,MA0ExBF,GAMAmC,QAAS,EAETlC,WAAY,SAAS/B,GAEjB,IAAIyF,GAAQ,EAMZ,MAJI,0BAA0B9G,KAAKqB,EAAE5B,OAAS0D,EAAamC,UACvDwB,GAAQ,GAGLA,GAEXjD,cAAe,SAASxC,GACpB,IAAI0F,EAAQ1F,EAAE5B,KAEA,eAAVsH,EAEA5D,EAAamC,SAAW,EAEjB,2BAA2BtF,KAAK+G,IAEvC1E,WAAW,WACHc,EAAamC,UACbnC,EAAamC,SAAW,IAE7B,OAoPf,OApIA3G,EAAMqI,KAAO,SAASC,GAClB,IAAIC,EAAOvC,SAASuC,KAIhB,aAFJD,EAAUA,SAGN7E,EAAOD,SAAW8E,EAAQ9E,UAG1B,UAAW8E,IACX7E,EAAOL,MAAQkF,EAAQlF,OAGvB2C,IACAwC,EAAK1C,iBAAiB,aAAcb,GAAY,GAChDuD,EAAK1C,iBAAiB,cAAerB,EAAaU,eAAe,GACjEqD,EAAK1C,iBAAiB,WAAYrB,EAAaU,eAAe,IAGlEqD,EAAK1C,iBAAiB,YAAab,GAAY,IASnDhF,EAAMwI,OAAS,SAASC,EAAUC,GAE9BD,EAAWzH,EAAiByH,GAEG,mBAA3BtH,EAASlB,KAAKyI,KACdA,EAAUA,EAAQC,KAAK,MAG3BD,EAAUA,EAAU,IAAMA,EAAU,GAIpC,IAAK,IAFDhE,EAASgD,EAEJN,EAAI,EAAGC,EAAMoB,EAAS7B,OAAQQ,EAAIC,EAAKD,IAG5CM,GADAhD,EAAU+D,EAASrB,IACDM,QAAQC,eAEiB,KAAtC,QAAS,OAAOiB,QAAQlB,KACzBJ,EAAWI,GAAShD,GACpBA,EAAUA,EAAQG,gBAG6B,IAA/CH,EAAQ8B,UAAUoC,QAAQ,kBAC1BlE,EAAQ8B,WAAa,gBAAkBkC,IASnD1I,EAAM4C,OAAS,SAAS6F,EAAUH,GAE9B,IAAIO,GADJJ,EAAWzH,EAAiByH,IACD7B,OAO3B,GALA0B,EAAmBA,MACnBA,EAAQQ,KAAWR,EAAQQ,MAAQ,EACnCR,EAAQS,SAAWT,EAAQS,UAAY,KAGnCF,EAYA,IAXA,IAAInE,EAASgC,EAAKsC,EAAKC,KAAa7B,EAAI,EACpC8B,GACApI,KAAM,YACNwF,OAAQ,GAQLc,EAAIyB,EAAazB,IAgBpB,GAfA1C,EAAU+D,EAASrB,GACnBV,EAAM4B,EAAQS,WACVI,EAAGzE,EAAQqC,YAAc,EACzBqC,EAAG1E,EAAQ2E,aAAe,GAG9BL,EAAWzH,EAAOmD,GAClBuE,EAAOE,EAAIH,EAAIpH,KAAO8E,EAAIyC,EAC1BF,EAAOG,EAAIJ,EAAIrH,IAAM+E,EAAI0C,EAEzBF,EAAUpC,MAAQmC,EAAOE,EACzBD,EAAUrC,MAAQoC,EAAOG,EAEzB3F,EAAO4B,KAAK6D,EAAWxE,GAEnB4D,EAAQQ,MAAQ,GAAsB,OAAjBR,EAAQQ,KAAe,CAC5C,IAAIQ,GACAxI,KAAM,UACNwF,OAAQ,GAGZ5C,WA5BS,SAAS4F,EAAS5E,GAC/B,OAAO,WACHjB,EAAOgC,KAAK6D,EAAS5E,IA0BV6E,CAAWD,EAAS5E,GAAU4D,EAAQQ,QASjE9I,EAAMwJ,KAAO,SAASf,GAOlB,IAAK,IALDa,GACAxI,KAAM,UACNwF,OAAQ,GAGHc,EAAI,EAAGC,GANhBoB,EAAWzH,EAAiByH,IAMG7B,OAAQQ,EAAIC,EAAKD,IAC5C3D,EAAOgC,KAAK6D,EAASb,EAASrB,KAOtCpH,EAAMyJ,cAAgB,SAASnB,GAE3BtI,EAAMqI,KAAKC,IAGRtI", "file": "waves.min.js"}