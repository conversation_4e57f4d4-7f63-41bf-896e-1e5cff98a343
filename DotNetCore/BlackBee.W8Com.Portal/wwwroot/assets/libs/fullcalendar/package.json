{"name": "fullcalendar", "version": "6.1.8", "title": "FullCalendar Standard Bundle", "description": "Easily render a full-sized drag & drop calendar with a combination of standard plugins", "homepage": "https://fullcalendar.io/docs/initialize-globals", "dependencies": {"@fullcalendar/core": "~6.1.8", "@fullcalendar/daygrid": "~6.1.8", "@fullcalendar/interaction": "~6.1.8", "@fullcalendar/list": "~6.1.8", "@fullcalendar/multimonth": "~6.1.8", "@fullcalendar/timegrid": "~6.1.8"}, "type": "module", "keywords": ["calendar", "event", "full-sized", "fullcalendar"], "bugs": "https://fullcalendar.io/reporting-bugs", "repository": {"type": "git", "url": "https://github.com/fullcalendar/fullcalendar.git", "directory": "bundle"}, "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://arshaw.com/"}, "copyright": "2023 <PERSON>", "types": "./index.d.ts", "main": "./index.cjs", "module": "./index.js", "unpkg": "./index.global.min.js", "jsdelivr": "./index.global.min.js", "exports": {"./package.json": "./package.json", "./index.cjs": "./index.cjs", "./index.js": "./index.js", ".": {"types": "./index.d.ts", "require": "./index.cjs", "import": "./index.js"}}}