{"Logging": {"LogLevel": {"Default": "Information", "Hangfire": "Warning", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File"], "MinimumLevel": {"Default": "Information", "Override": {"Hangfire": "Warning", "Microsoft": "Error", "Microsoft.Hosting.Lifetime": "Information", "System": "Information"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"theme": "Serilog.Sinks.SystemConsole.Themes.AnsiConsoleTheme::Code, Serilog.Sinks.Console", "formatter": "Serilog.Formatting.Compact.RenderedCompactJsonFormatter, Serilog.Formatting.Compact", "rollingInterval": "Day", "restrictedToMinimumLevel": "Information", "retainedFileCountLimit": 5}}, {"Name": "File", "Args": {"path": "Log\\W8AuthPortal.txt", "rollingInterval": "Day", "restrictedToMinimumLevel": "Error", "enrich": ["WithMachineName"]}}, {"Name": "Hangfire"}, {"Name": "Elasticsearch", "Args": {"nodeUris": "http://localhost:9200;", "indexFormat": "FSH.WebApi-logs-{0:yyyy.MM}", "numberOfShards": 2, "numberOfReplicas": 1, "restrictedToMinimumLevel": "Information"}}], "Enrich": ["FromLogContext", "WithMachineName", "WithProcessId", "WithThreadId", "WithHangfireContext"], "Properties": {"Application": "BlackBee.W8Auth.Portal"}}}