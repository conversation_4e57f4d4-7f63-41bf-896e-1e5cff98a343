using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.Configuration;

namespace BlackBee.W8Com.Portal.Configurations;

internal static class Startup
{
    internal static ConfigureHostBuilder AddConfigurations(this ConfigureHostBuilder host)
    {
        host.ConfigureAppConfiguration((context, config) =>
        {
            const string configurationsDirectory = "Configurations";
            var env = context.HostingEnvironment;
            config.AddJsonFile("appsettings.json", optional: false, reloadOnChange: false)
                .AddJsonFile($"appsettings.{env.EnvironmentName}.json", optional: true, reloadOnChange: false)
                .AddJsonFile($"{configurationsDirectory}/logger.json", optional: false, reloadOnChange: false)
                .AddJsonFile($"{configurationsDirectory}/logger.{env.EnvironmentName}.json", optional: true, reloadOnChange: false)
                .AddJsonFile($"{configurationsDirectory}/cache.json", optional: false, reloadOnChange: false)
                .AddJsonFile($"{configurationsDirectory}/cache.{env.EnvironmentName}.json", optional: true, reloadOnChange: false)
                .AddJsonFile($"{configurationsDirectory}/mail.json", optional: false, reloadOnChange: false)
                .AddJsonFile($"{configurationsDirectory}/mail.{env.EnvironmentName}.json", optional: true, reloadOnChange: false)
                .AddJsonFile($"{configurationsDirectory}/security.json", optional: false, reloadOnChange: false)
                .AddJsonFile($"{configurationsDirectory}/security.{env.EnvironmentName}.json", optional: true, reloadOnChange: false)
				.AddEnvironmentVariables();
        });
        return host;
    }
}