using BlackBee.W8Auth.Api.Wrapper;
using BlackBee.W8Base.Api.Model.Data;
using BlackBee.W8Base.Api.Wrapper.Exception;
using BlackBee.W8Base.Caching;
using BlackBee.W8Base.Handler;
using BlackBee.W8Base.Web.Auth.Jwt;
using BlackBee.W8Base.Web.Controllers;
using BlackBee.W8Com.Portal.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;

namespace BlackBee.W8Gear.Portal.Controllers
{
    public class LegalController : ServiceBindableController
    {
        private readonly W8AuthService _authService;
        private readonly W8AuthenticationHelper<BearableService> _helper;
        private readonly SiteSettings _settings;
        private readonly IConfiguration _configuration;

        public LegalController(
            IServiceProvider serviceProvider,
            ILogger<AuthenticationController> logger,
            IConfiguration configuration,
            ICacheService cache,
            W8AuthService authService, W8AuthenticationHelper<BearableService> helper, SiteSettings settings) : base(serviceProvider)
        {
            _helper = helper;
            _settings = settings;
            _authService = authService;
            _configuration = configuration;
        }

        [AllowAnonymous]
        [HttpPost]
        public async Task<IActionResult> Agree(string email, Guid uid)
        {
            if (string.IsNullOrEmpty(email))
            {
                return Json("The email address is empty");
            }

            try
            {
                var user = await _authService.GetUserAsync(uid, false);

                if (user is { Success: true })
                {
                    if (user.ApplicationUser.Email.ToUpper() != email.ToUpper())
                    {
                        return Json("The email address is not valid");
                    }
                }
                else
                {
                    if (user != null)
                    {
                        string s = "";

                        foreach (DataError responseError in user.Errors)
                        {
                            s += responseError.Message + "\n";
                        }

                        return Json(s);
                    }

                    return Json("Invalid Response from server. Please try again.");
                }

                var response = await _authService.UserAcceptsTermsAsync(uid);

                if (response is { Success: true })
                {
                    return Json("Success");
                }

                if (response != null)
                {
                    string s = "";

                    foreach (DataError responseError in response.Errors)
                    {
                        s += responseError.Message + "\n";
                    }

                    return Json(s);
                }

                return Json("Invalid Response from server. Please try again.");
            }
            catch (WrapperException cex)
            {
                string s = "";

                foreach (DataError responseError in cex.Errors)
                {
                    s += responseError.Message + "\n";
                }

                return Json(s);
            }
            catch (Exception ex)
            {
                return Json(ex.Message);
            }
        }

        [AllowAnonymous]
        [HttpPost]
        public async Task<IActionResult> Sign(string email, Guid uid)
        {
            if (string.IsNullOrEmpty(email))
            {
                return Json("The email address is empty");
            }

            try
            {
                var user = await _authService.GetUserAsync(uid, false);

                if (user is { Success: true })
                {
                    if (user.ApplicationUser.Email.ToUpper() != email.ToUpper())
                    {
                        return Json("The email address is not valid");
                    }
                }
                else
                {
                    if (user != null)
                    {
                        string s = "";

                        foreach (DataError responseError in user.Errors)
                        {
                            s += responseError.Message + "\n";
                        }

                        return Json(s);
                    }

                    return Json("Invalid Response from server. Please try again.");
                }

                var response = await _authService.UserSignsPolicyAsync(uid);

                if (response is { Success: true })
                {
                    return Json("Success");
                }

                if (response != null)
                {
                    string s = "";

                    foreach (DataError responseError in response.Errors)
                    {
                        s += responseError.Message + "\n";
                    }

                    return Json(s);
                }

                return Json("Invalid Response from server. Please try again.");
            }
            catch (WrapperException cex)
            {
                string s = "";

                foreach (DataError responseError in cex.Errors)
                {
                    s += responseError.Message + "\n";
                }

                return Json(s);
            }
            catch (Exception ex)
            {
                return Json(ex.Message);
            }
        }

        [AllowAnonymous]
        [HttpPost]
        public async Task<JsonResult> Decline(Guid id)
        {
            try
            {
                await _helper.LogOut(HttpContext);
                return Json("Success");
            }
            catch (WrapperException cex)
            {
                OnServiceWrapperException(cex);
            }
            catch (Exception ex)
            {
                OnException(ex);
            }

            return Json("Error");
        }
    }
}
