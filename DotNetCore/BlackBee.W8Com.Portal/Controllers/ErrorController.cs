using System;
using BlackBee.W8Base.Web.Controllers;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace BlackBee.W8Com.Portal.Controllers
{
    public class ErrorController : ServiceBindableController
    {
        public ErrorController(IServiceProvider serviceProvider) : base(serviceProvider)
        {
        }

        [AllowAnonymous]
        [ActionName("Errors401")]
        public IActionResult Errors401()
        {
            return View();
        }

        [AllowAnonymous]
        [ActionName("Errors404")]
        public IActionResult Errors404()
        {
            return View();
        }

        [AllowAnonymous]
        [ActionName("Errors500")]
        public IActionResult Errors500()
        {
            return View();
        }

        [AllowAnonymous]
        [ActionName("Offline")]
        public IActionResult Offline()
        {
            return View();
        }


    }
}
