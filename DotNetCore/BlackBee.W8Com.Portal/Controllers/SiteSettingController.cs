using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using BlackBee.W8Auth.Api.Model.Models.ConfigurationSetting;
using BlackBee.W8Auth.Api.Wrapper;
using BlackBee.W8Base.Api.Model.Data;
using BlackBee.W8Base.Api.Model.Enums;
using BlackBee.W8Base.Api.Model.Models;
using BlackBee.W8Base.Api.Wrapper.Exception;
using BlackBee.W8Base.Caching;
using BlackBee.W8Base.Handler;
using BlackBee.W8Base.Web.Auth.Jwt;
using BlackBee.W8Base.Web.Controllers;
using BlackBee.W8Com.Portal.Models;
using BlackBee.W8Gear.Portal.Controllers;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace BlackBee.W8Com.Portal.Controllers
{
    public class SiteSettingController : ServiceBindableController
    {
        private readonly W8AuthService _authService;
        private readonly W8AuthenticationHelper<BearableService> _helper;
        private readonly SiteSettings _settings;
        private readonly IConfiguration _configuration;

        public SiteSettingController(
            IServiceProvider serviceProvider,
            ILogger<AuthenticationController> logger,
            IConfiguration configuration,
            ICacheService cache,
            W8AuthService authService, W8AuthenticationHelper<BearableService> helper, SiteSettings settings) : base(serviceProvider)
        {
            _helper = helper;
            _settings = settings;
            _authService = authService;
            _configuration = configuration;
        }


        [AllowAnonymous]
        [HttpPost]
        public async Task<JsonResult> LoadSettings()
        {
            var email = GetUserEmail();
            var userId = GetUserId();

            if (userId == null || userId == Guid.Empty)
            {
                return Json("Error");
            }

            if (string.IsNullOrEmpty(email))
            {
                return Json("Error");
            }

            try
            {
                var body = new SearchCriteriaModel
                {
                    AscendingOrder = true,
                    FilterJoin = FilterJoin.And,
                    SortKey = "Group",
                    Filters = new List<FilterModel>()
                    {
                        new FilterModel()
                        {
                            Value = "PORTAL.LAYOUT.SETTINGS",
                            Operation = Operation.Equals,
                            PropertyName = "Group"
                        },
                        new FilterModel()
                        {
                            Value = email.ToUpper().Trim(),
                            Operation = Operation.Equals,
                            PropertyName = "Key"
                        }
                    }
                };

                var response = await _authService.SearchConfigurationSettingsAsync(body, 1, 1);

                if (response is { Success: true })
                {
                    OnServiceOk();

                    var item = response.ConfigurationSettings.PageResult.FirstOrDefault();

                    if (item != null)
                    {
                        return Json(item.Value);
                    }
                    else
                    {
                        return Json("No Settings");
                    }
                }

                StringBuilder sb = new StringBuilder();

                if (response?.Errors != null)
                {
                    foreach (DataError responseError in response.Errors)
                    {
                        sb.Append(responseError.Message);
                    }

                    return Json("Error " + sb.ToString());
                }
            }
            catch (WrapperException cex)
            {
                StringBuilder sb = new StringBuilder();

                foreach (DataError responseError in cex.Errors)
                {
                    sb.Append(responseError.Message);
                }

                return Json("Error " + sb.ToString());
            }
            catch (Exception ex)
            {
                return Json("Error " + ex.Message);
            }

            return Json("Error");
        }

        [AllowAnonymous]
        [HttpPost]
        public async Task<JsonResult> SaveSettings(string settings)
        {
            var email = GetUserEmail();
            var userId = GetUserId();

            if (userId == null || userId == Guid.Empty)
            {
                return Json("Error");
            }

            if (string.IsNullOrEmpty(email))
            {
                return Json("Error");
            }

            try
            {
                ConfigurationSettingPostModel model = new ConfigurationSettingPostModel()
                {
                    CreatedByUserId = userId,
                    Value = settings,
                    Group = "PORTAL.LAYOUT.SETTINGS",
                    Key = email.ToUpper().Trim()
                };

                var response = await _authService.PostConfigurationSettingAsync(model);

                if (response is { Success: true })
                {
                    OnServiceOk();

                    return Json("Success");
                }

                StringBuilder sb = new StringBuilder();

                if (response?.Errors != null)
                {
                    foreach (DataError responseError in response.Errors)
                    {
                        sb.Append(responseError.Message);
                    }

                    return Json(sb.ToString());
                }
            }
            catch (WrapperException cex)
            {
                StringBuilder sb = new StringBuilder();

                foreach (DataError responseError in cex.Errors)
                {
                    sb.Append(responseError.Message);
                }

                return Json(sb.ToString());
            }
            catch (Exception ex)
            {
                return Json(ex.Message);
            }

            return Json("Error");
        }
    }
}
