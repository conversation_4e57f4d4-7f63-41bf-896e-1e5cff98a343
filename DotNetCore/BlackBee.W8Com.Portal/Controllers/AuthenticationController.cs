using BlackBee.W8Auth.Api.Model.Models.Identity.Request;
using BlackBee.W8Auth.Api.Wrapper;
using BlackBee.W8Base.Api.Model.Enums;
using BlackBee.W8Base.Api.Model.Models;
using BlackBee.W8Base.Api.Wrapper.Exception;
using BlackBee.W8Base.Auth;
using BlackBee.W8Base.Caching;
using BlackBee.W8Base.Handler;
using BlackBee.W8Base.Web.Auth.Jwt;
using BlackBee.W8Base.Web.Controllers;
using BlackBee.W8Com.Portal.Models;
using BlackBee.W8Com.Portal.ViewModels.Authentication;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Net.Http;
using System.Security.Claims;
using System.Threading.Tasks;
using BlackBee.W8Com.Portal.Helper.Authentication;

namespace BlackBee.W8Gear.Portal.Controllers
{
    public class AuthenticationController : ServiceBindableController
    {
        private readonly W8AuthService _authService;
        private readonly W8AuthenticationHelper<BearableService> _helper;
        private readonly SiteSettings _settings;
        private readonly IConfiguration _configuration;

        public AuthenticationController(
            IServiceProvider serviceProvider,
            ILogger<AuthenticationController> logger,
            IConfiguration configuration,
            ICacheService cache,
            W8AuthService authService, W8AuthenticationHelper<BearableService> helper, SiteSettings settings) : base(serviceProvider)
        {
            _helper = helper;
            _settings = settings;
            _authService = authService;
            _configuration = configuration;
        }

        [AllowAnonymous]
        public async Task<IActionResult> SignInBasic(Guid? rjvId)
        {
            //This is for redirection from central where the token is stored for 1 minute and contains the access tokens
            if (rjvId != null)
            {
                try
                {
                    bool success = await _helper.LogIn(HttpContext, rjvId.Value);

                    if (success)
                    {
                        OnServiceOk();

                        TempData["RefreshSettings"] = true;
                        return RedirectToAction("Index", "Dashboard");
                    }

                    ModelState.AddModelError("db_msg", "Could not Sign On with Rejuvenated Id");
                }
                catch (HttpRequestException ex)
                {
                    ModelState.AddModelError("db_msg", WriteHttpException(ex, "W8Auth"));
                }
                catch (ValidationException ex)
                {
                    ModelState.AddModelError("db_msg", WriteDbValidationException(ex));
                }
                catch (WrapperException cex)
                {
                    OnServiceWrapperException(cex);
                }
                catch (Exception ex)
                {
                    OnException(ex);
                }
            }

            LoginViewModel model = new LoginViewModel();
            return View(model);
        }

        [AllowAnonymous]
        [HttpPost]
        public async Task<IActionResult> SignInBasic(LoginViewModel model)
        {
            if (!ModelState.IsValid)
            {
                ModelState.AddModelError("ex_msg", "Model is not valid");
                return View(model);
            }

            try
            {
                bool success = await _helper.LogIn(HttpContext, model.Email, model.Password);

                if (success)
                {
                    OnServiceOk();

                    TempData["RefreshSettings"] = true;
                    return RedirectToAction("Index", "Dashboard");
                }

                ModelState.AddModelError("db_msg", "Could not Sign On");
            }
            catch (HttpRequestException ex)
            {
                ModelState.AddModelError("db_msg", WriteHttpException(ex, "W8Auth"));
            }
            catch (ValidationException ex)
            {
                ModelState.AddModelError("db_msg", WriteDbValidationException(ex));
            }
            catch (WrapperException cex)
            {
                OnServiceWrapperException(cex);
            }
            catch (Exception ex)
            {
                OnException(ex);
            }

            return View(model);
        }

        [AllowAnonymous]
        public IActionResult SignUpBasic()
        {
            return View();
        }

        [AllowAnonymous]
        [HttpPost]
        public async Task<IActionResult> SignUpBasic(RegisterViewModel vm)
        {
            if (!ModelState.IsValid)
            {
                ModelState.AddModelError("ex_msg", "Model is not valid");
                return View(vm);
            }

            try
            {
                RegistrationRequestModel model = ModelHelper.ToRegisterModel(vm);
                var response = await _authService.RegisterUserAsync(model);

                if (response is { Success: true })
                {
                    OnServiceOk();

                    TempData["IsSaved"] = true;
                    return RedirectToAction("SignInBasic");
                }

                OnServiceNotOk(response);
            }
            catch (HttpRequestException ex)
            {
                ModelState.AddModelError("db_msg", WriteHttpException(ex, "W8Auth"));
            }
            catch (ValidationException ex)
            {
                ModelState.AddModelError("db_msg", WriteDbValidationException(ex));
            }
            catch (WrapperException cex)
            {
                OnServiceWrapperException(cex);
            }
            catch (Exception ex)
            {
                OnException(ex);
            }

            return View(vm);
        }

        [AllowAnonymous]
        public async Task<IActionResult> ResetPasswordBasic(string token)
        {
            var vm = new ResetPasswordViewModel { Token = token };
            return View(vm);
        }

        [AllowAnonymous]
        [HttpPost]
        public async Task<IActionResult> ResetPasswordBasic(ResetPasswordViewModel vm)
        {
            if (!ModelState.IsValid)
            {
                ModelState.AddModelError("ex_msg", "Model is not valid");
                return View(vm);
            }

            try
            {
                PasswordResetRequestModel model = ModelHelper.ToPatchModel(vm);
                var response = await _authService.ResetPasswordAsync(model, vm.Email);

                if (response is { Success: true })
                {
                    OnServiceOk();

                    TempData["IsSaved"] = true;
                    return RedirectToAction("SignInBasic");
                }

                OnServiceNotOk(response);
            }
            catch (WrapperException cex)
            {
                OnServiceWrapperException(cex);
            }
            catch (Exception ex)
            {
                OnException(ex);
            }

            return View(vm);
        }

        [AllowAnonymous]
        public async Task<IActionResult> ForgotPasswordBasic()
        {
            var vm = new ForgotPasswordViewModel { };
            return View(vm);
        }

        [AllowAnonymous]
        [HttpPost]
        public async Task<IActionResult> ForgotPasswordBasic(ForgotPasswordViewModel vm)
        {
            if (!ModelState.IsValid)
            {
                ModelState.AddModelError("ex_msg", "Model is not valid");
                return View(vm);
            }

            try
            {
                var pm = new PasswordForgotRequestModel()
                {
                    CallbackUrl = $"{_settings.Url}/authentication/resetpasswordbasic",
                    SendMail = true
                };

                var response = await _authService.ForgotPasswordAsync(pm, vm.Email);

                if (response is { Success: true })
                {
                    OnServiceOk();

                    TempData["EmailSent"] = true;
                    return RedirectToAction("SignInBasic");
                }

                OnServiceNotOk(response);
            }
            catch (WrapperException cex)
            {
                OnServiceWrapperException(cex);
            }
            catch (Exception ex)
            {
                OnException(ex);
            }

            return View(vm);
        }

        public IActionResult LockScreenBasic()
        {
            LoginViewModel model = new LoginViewModel
            {
                Email = GetUserEmail() ?? ""
            };

            return View(model);
        }

        [AllowAnonymous]
        [HttpPost]
        public async Task<IActionResult> LockScreenBasic(LoginViewModel model)
        {
            if (!ModelState.IsValid)
            {
                ModelState.AddModelError("ex_msg", "Model is not valid");
                return View(model);
            }

            try
            {
                var securitySettings = _configuration.GetSection("SecuritySettings").Get<SecuritySettings>();

                if (securitySettings == null)
                {
                    ModelState.AddModelError("ex_msg", "Security Settings Invalid");
                    return View(model);
                }

                bool success = await _helper.LogIn(HttpContext, model.Email, model.Password);

                if (success)
                {
                    OnServiceOk();

                    TempData["RefreshSettings"] = true;
                    return RedirectToAction("Index", "Dashboard");
                }

                ModelState.AddModelError("db_msg", "Could not Sign On");
            }
            catch (HttpRequestException ex)
            {
                ModelState.AddModelError("db_msg", WriteHttpException(ex, "W8Auth"));
            }
            catch (ValidationException ex)
            {
                ModelState.AddModelError("db_msg", WriteDbValidationException(ex));
            }
            catch (WrapperException cex)
            {
                OnServiceWrapperException(cex);
            }
            catch (Exception ex)
            {
                OnException(ex);
            }

            return View(model);
        }


        public async Task<IActionResult> LogoutBasic()
        {
            await _helper.LogOut(HttpContext);

            return RedirectToAction("SignInBasic");
        }

        public async Task<IActionResult> RedirectToCentral()
        {
            try
            {
                SearchCriteriaModel model = new SearchCriteriaModel()
                {
                    AscendingOrder = true,
                    FilterJoin = FilterJoin.And,
                    SortKey = "UniqueReference",
                    Filters = new List<FilterModel>()
                    {
                        new FilterModel()
                        {
                            Operation = Operation.Contains,
                            PropertyName = "UniqueReference",
                            Value = "CENTRAL"
                        }
                    }
                };

                var response = await _authService.SearchApplicationInstancesAsync(model, 1, 1);

                if (response is { Success: true })
                {
                    OnServiceOk();

                    var applicationInstance = response.ApplicationInstances.PageResult.FirstOrDefault();
                    Claim? claim = User.Claims.FirstOrDefault((Claim? c) => c.Type.ToUpper() == "EMAIL");

                    if (applicationInstance != null && claim != null)
                    {
                        var guid = await _helper.Rejuvenate(HttpContext, claim.Value, applicationInstance.UniqueReference);

                        if (guid != Guid.Empty)
                        {
                            var url = $"{applicationInstance.SiteUrl}?rjvId={guid}";
                            return new RedirectResult(url);
                        }
                    }
                }

                OnServiceNotOk(response);
            }
            catch (WrapperException cex)
            {
                OnServiceWrapperException(cex);
            }
            catch (Exception ex)
            {
                OnException(ex);
            }

            return RedirectToAction("Index", "Dashboard");
        }
    }
}
