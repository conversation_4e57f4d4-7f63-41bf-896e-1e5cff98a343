using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using BlackBee.W8Auth.Api.Wrapper;
using BlackBee.W8Base.Api.Model.Enums;
using BlackBee.W8Base.Api.Model.Models;
using BlackBee.W8Base.Api.Wrapper.Exception;
using BlackBee.W8Base.Caching;
using BlackBee.W8Base.Web.Attributes;
using BlackBee.W8Com.Api.Model.Models.ConfigurationSetting;
using BlackBee.W8Com.Api.Wrapper;
using BlackBee.W8Com.Portal.Controllers.Base;
using BlackBee.W8Com.Portal.Helper;
using BlackBee.W8Com.Portal.Manager;
using BlackBee.W8Com.Portal.Models;
using BlackBee.W8Com.Portal.ViewModels.ConfigurationSetting;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;

namespace BlackBee.W8Com.Portal.Controllers
{
    public class ConfigurationSettingController : W8PortalBaseController
    {
        private IStringLocalizer<ConfigurationSettingController> _localizer;
        private W8ComService _gearService;

        public ConfigurationSettingController(
            IServiceProvider serviceProvider,
            ILogger<ConfigurationSettingController> logger,
            IConfiguration configuration,
            ICacheService cache,
            W8AuthService authService,
            W8ComService gearService,
            IStringLocalizer<ConfigurationSettingController> localizer) : base(serviceProvider, logger, configuration, cache, authService)
        {
            _localizer = localizer;
            _gearService = gearService;
        }

        [AuthorizeRoles("~/Authentication/SignInBasic", "~/Error/Errors401", ConfigurationSettingRoles.Search)]
        public async Task<IActionResult> Index()
        {
            var list = new List<ConfigurationSettingSearchModel>();

            var body = new SearchCriteriaModel
            {
                Filters = new List<FilterModel>(),
                AscendingOrder = true,
                FilterJoin = FilterJoin.And,
                SortKey = "Key"
            };

            try
            {
                int rows = 500;
                int index = 1;
                var response = await _gearService.SearchConfigurationSettingsAsync(body, index, rows);

                if (response is { Success: true })
                {
                    OnServiceOk();
                    
                    list.AddRange(response.ConfigurationSettings.PageResult);

                    while (response is { Success: true, ConfigurationSettings.HasNextPage: true })
                    {
                        index += 500;
                        response = await _gearService.SearchConfigurationSettingsAsync(body, index, rows);

                        if (response is { Success: true })
                        {
                            list.AddRange(response.ConfigurationSettings.PageResult);
                        }
                    }

                    return View(list);
                }

                OnServiceNotOk(response);
            }
            catch (WrapperException cex)
            {
                OnServiceWrapperException(cex);
            }
            catch (Exception ex)
            {
                OnException(ex);
            }

            return View(list);
        }

        [AuthorizeRoles("~/Authentication/SignInBasic", "~/Error/Errors401", ConfigurationSettingRoles.Create)]
        public async Task<IActionResult> Create()
        {
            var vm = new ConfigurationSettingViewModel { ViewMode = ViewMode.Create, Id = Guid.Empty, Group = string.Empty, Key = string.Empty, Value = string.Empty };

            Guid? uid = GetUserId();
            if (uid != null)
            {
                vm.CreatedByUserId = uid.Value;
            }

            return View(vm);
        }

        [AuthorizeRoles("~/Authentication/SignInBasic", "~/Error/Errors401", ConfigurationSettingRoles.Create)]
        [HttpPost]
        public async Task<IActionResult> Create(ConfigurationSettingViewModel vm)
        {
            ClearModelState();

            if (!ModelState.IsValid)
            {
                await Enrich(vm);
                return View(vm);
            }

            try
            {
                ConfigurationSettingPostModel model = ModelHelper.ToPostModel(vm);
                var response = await _gearService.PostConfigurationSettingAsync(model);

                if (response is { Success: true })
                {
                    TempData["IsSaved"] = true;
                    return RedirectToAction("Index");
                }

                OnServiceNotOk(response);
            }
            catch (WrapperException cex)
            {
                OnServiceWrapperException(cex);
            }
            catch (Exception ex)
            {
                OnException(ex);
            }

            await Enrich(vm);
            TempData["IsSaved"] = false;
            return View(vm);
        }

        [AuthorizeRoles("~/Authentication/SignInBasic", "~/Error/Errors401", ConfigurationSettingRoles.Update)]
        public async Task<IActionResult> Edit(Guid id)
        {
            var vm = new ConfigurationSettingViewModel { ViewMode = ViewMode.Create, Id = Guid.Empty, Group = string.Empty, Key = string.Empty, Value = string.Empty };

            try
            {
                var response = await _gearService.GetConfigurationSettingAsync(id, true);

                if (response is { Success: true, ConfigurationSetting: { } })
                {
                    OnServiceOk();
                    
                    vm = ModelHelper.FromModel(response.ConfigurationSetting);
                    vm.ViewMode = ViewMode.Update;

                    Guid? uid = GetUserId();
                    if (uid != null)
                    {
                        vm.ModifiedByUserId = uid.Value;
                    }
                }
                else
                {
                    ModelState.AddModelError("ex_msg", $"The service could not load the entity with id {id}. Please try again.");
                }
            }
            catch (WrapperException cex)
            {
                OnServiceWrapperException(cex);
            }
            catch (Exception ex)
            {
                OnException(ex);
            }

            await Enrich(vm);
            return View(vm);
        }

        [AuthorizeRoles("~/Authentication/SignInBasic", "~/Error/Errors401", ConfigurationSettingRoles.Update)]
        [HttpPost]
        public async Task<IActionResult> Edit(ConfigurationSettingViewModel vm)
        {
            if (vm == null)
            {
                ModelState.AddModelError("ex_msg", "Model is Null");
                return View();
            }

            ClearModelState();

            if (!ModelState.IsValid)
            {
                await Enrich(vm);
                return View(vm);
            }

            try
            {
                ConfigurationSettingPutModel model = ModelHelper.ToPutModel(vm);
                var response = await _gearService.PutConfigurationSettingAsync(model);

                if (response is { Success: true })
                {
                    OnServiceOk();
                    
                    TempData["IsSaved"] = true;
                    return RedirectToAction("Index");
                }

                OnServiceNotOk(response);
            }
            catch (WrapperException cex)
            {
                OnServiceWrapperException(cex);
            }
            catch (Exception ex)
            {
                OnException(ex);
            }

            await Enrich(vm);
            TempData["IsSaved"] = false;
            return View(vm);
        }

        [AuthorizeRoles("~/Authentication/SignInBasic", "~/Error/Errors401", ConfigurationSettingRoles.Delete)]
        public async Task<IActionResult> Delete(Guid id)
        {
            var vm = new ConfigurationSettingViewModel { ViewMode = ViewMode.Create, Id = Guid.Empty, Group = string.Empty, Key = string.Empty, Value = string.Empty };

            try
            {
                var response = await _gearService.GetConfigurationSettingAsync(id, false);

                if (response is { Success: true, ConfigurationSetting: { } })
                {
                    OnServiceOk();
                    
                    vm = ModelHelper.FromModel(response.ConfigurationSetting);
                    vm.ViewMode = ViewMode.Delete;

                    Guid? uid = GetUserId();
                    if (uid != null)
                    {
                        vm.ModifiedByUserId = uid.Value;
                    }
                }
                else
                {
                    ModelState.AddModelError("ex_msg", $"The service could not load the entity with id {id}. Please try again.");
                }
            }
            catch (WrapperException cex)
            {
                OnServiceWrapperException(cex);
            }
            catch (Exception ex)
            {
                OnException(ex);
            }

            await Enrich(vm);
            return View(vm);
        }

        [AuthorizeRoles("~/Authentication/SignInBasic", "~/Error/Errors401", ConfigurationSettingRoles.Delete)]
        [HttpPost]
        public async Task<IActionResult> Delete(ConfigurationSettingViewModel vm)
        {
            if (vm == null)
            {
                ModelState.AddModelError("ex_msg", "Model is Null");
                return View();
            }

            ClearModelState();

            if (!ModelState.IsValid)
            {
                await Enrich(vm);
                return View(vm);
            }

            try
            {
                var response = await _gearService.DeleteConfigurationSettingAsync(vm.Id);

                if (response is { Success: true })
                {
                    OnServiceOk();
                    
                    return RedirectToAction("Index");
                }

                OnServiceNotOk(response);
            }
            catch (WrapperException cex)
            {
                OnServiceWrapperException(cex);
            }
            catch (Exception ex)
            {
                OnException(ex);
            }

            await Enrich(vm);
            return View(vm);
        }

        [AuthorizeRoles("~/Authentication/SignInBasic", "~/Error/Errors401", ConfigurationSettingRoles.Read)]
        public async Task<IActionResult> Read(Guid id)
        {
            var vm = new ConfigurationSettingViewModel { ViewMode = ViewMode.Create, Id = Guid.Empty, Group = string.Empty, Key = string.Empty, Value = string.Empty };

            try
            {
                var response = await _gearService.GetConfigurationSettingAsync(id, true);

                if (response is { Success: true, ConfigurationSetting: { } })
                {
                    OnServiceOk();
                    
                    vm = ModelHelper.FromModel(response.ConfigurationSetting);
                    vm.ViewMode = ViewMode.Read;
                }
                else
                {
                    ModelState.AddModelError("ex_msg", $"The service could not load the entity with id {id}. Please try again.");
                }
            }
            catch (WrapperException cex)
            {
                OnServiceWrapperException(cex);
            }
            catch (Exception ex)
            {
                OnException(ex);
            }

            await Enrich(vm);
            return View(vm);
        }

        private async Task Enrich(ConfigurationSettingViewModel model)
        {
            if (model.Id != Guid.Empty)
            {

            }
        }

        private void ClearModelState()
        {

        }
    }
}
