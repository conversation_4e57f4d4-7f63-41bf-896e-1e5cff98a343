using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using BlackBee.W8Auth.Api.Model.Models.SystemTimezone;
using BlackBee.W8Auth.Api.Wrapper;
using BlackBee.W8Base.Api.Model.Data;
using BlackBee.W8Base.Api.Model.Enums;
using BlackBee.W8Base.Api.Model.Models;
using BlackBee.W8Base.Caching;
using BlackBee.W8Base.Handler;
using BlackBee.W8Base.Web.Auth.Jwt;
using BlackBee.W8Base.Web.Controllers;
using BlackBee.W8Com.Portal.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace BlackBee.W8Com.Portal.Controllers
{
    public class SystemTimezoneController : ServiceBindableController
    {
        private readonly W8AuthService _authService;
        private readonly W8AuthenticationHelper<BearableService> _helper;
        private readonly SiteSettings _settings;
        private readonly IConfiguration _configuration;

        public SystemTimezoneController(
            IServiceProvider serviceProvider,
            ILogger<SystemTimezoneController> logger,
            IConfiguration configuration,
            ICacheService cache,
            W8AuthService authService, W8AuthenticationHelper<BearableService> helper, SiteSettings settings) : base(serviceProvider)
        {
            _helper = helper;
            _settings = settings;
            _authService = authService;
            _configuration = configuration;
        }

        [AllowAnonymous]
        [Produces("application/json")]
        public async Task<IActionResult> Search(string q, int page)
        {
            try
            {
                var pageSize = 30;
                if (page == 0) page = 1;

                SystemTimezonesResponseModel? response = null;

                if (q.Length < 2)
                {
                    var sc = new SearchCriteriaModel()
                    {
                        AscendingOrder = true,
                        SortKey = "CountryDescription",
                        FilterJoin = FilterJoin.And
                    };

                    response = await _authService.SearchSystemTimezonesAsync(sc, page, pageSize);
                }
                else
                {
                    q = q.ToUpper();

                    var sc = new SearchCriteriaModel()
                    {
                        AscendingOrder = true,
                        SortKey = "CountryDescription",
                        FilterJoin = FilterJoin.And,
                        Filters = new List<FilterModel>()
                        {
                            new FilterModel()
                            {
                                Value = q,
                                Operation = Operation.Contains,
                                PropertyName = "CountryDescription"
                            }
                        }
                    };

                    response = await _authService.SearchSystemTimezonesAsync(sc, page, pageSize);
                }

                if (response is { Success: true })
                {
                    var count = response.SystemTimezones.ThisCount;
                    var items = response.SystemTimezones.PageResult;

                    var model = new Select2Model
                    {
                        pagination = new Pagination
                        {
                            page = page,
                            total_count = count + 1,
                            more = page * pageSize < count + 1
                        },

                        results = new List<Result>()
                    };

                    foreach (var item in items)
                        model.results.Add(new Result
                        {
                            id = item.CountryDescription,
                            text = item.CountryDescription,
                            description = item.CountryDescription
                        });

                    return Ok(model);
                }

                StringBuilder sb = new StringBuilder();

                if (response != null)
                    foreach (DataError responseError in response.Errors)
                    {
                        sb.Append(responseError.Message);
                        sb.Append(", ");
                    }

                return BadRequest(sb.ToString());
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }
    }
}
