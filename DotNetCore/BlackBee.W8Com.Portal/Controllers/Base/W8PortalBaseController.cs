using System;
using BlackBee.W8Auth.Api.Wrapper;
using BlackBee.W8Base.Caching;
using BlackBee.W8Base.Web.Controllers;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace BlackBee.W8Com.Portal.Controllers.Base
{
    public class W8PortalBaseController : ServiceBindableController
    {
        protected readonly ICacheService Cache;
        protected readonly IConfiguration Configuration;
        protected readonly ILogger<W8PortalBaseController> Logger;
        protected readonly W8AuthService AuthService;

        public W8PortalBaseController(
            IServiceProvider serviceProvider,
            ILogger<W8PortalBaseController> logger,
            IConfiguration configuration,
            ICacheService cache,
            W8AuthService authService) : base(serviceProvider)
        {
            Logger = logger;
            Configuration = configuration;
            Cache = cache;
            AuthService = authService;
        }

        protected string? AppName => Configuration.GetSection("SiteSettings").GetValue<string>("SiteName") ?? string.Empty;
        protected string? Platform => Configuration.GetSection("SiteSettings").GetValue<string>("Platform") ?? string.Empty;
        
    }
}
