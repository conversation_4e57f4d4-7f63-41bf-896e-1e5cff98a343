
using BlackBee.W8Auth.Api.Wrapper;
using BlackBee.W8Base.Caching;
using BlackBee.W8Base.Web.Attributes;
using BlackBee.W8Com.Api.Model.Models;  // Changed from SmsLog namespace
using BlackBee.W8Com.Portal.Controllers.Base;
using BlackBee.W8Com.Portal.Manager;
using BlackBee.W8Com.Portal.Models;
using BlackBee.W8Com.EF;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace BlackBee.W8Com.Portal.Controllers
{
    public class DashBoardController : W8PortalBaseController
    {
        private readonly IStringLocalizer<DashBoardController> _localizer;
        private const int PageSize = 10;

        public DashBoardController(IServiceProvider serviceProvider,
            ILogger<DashBoardController> logger,
            IConfiguration configuration,
            ICacheService cache,
            W8AuthService authService,
            IStringLocalizer<DashBoardController> localizer) : base(serviceProvider, logger, configuration, cache, authService)
        {
            _localizer = localizer;
        }

        [AuthorizeRoles("~/Authentication/SignInBasic", "~/Error/Errors401", DashBoardRoles.View)]
        [HttpGet]
        public async Task<IActionResult> Index(string? clientId = null, string? tenantId = null, string? startDate = null,
            string? endDate = null, bool showUnbilledOnly = false, int page = 1)
        {
            var viewModel = new SmsLogViewModel
            {
                ClientId = clientId ?? string.Empty,
                TenantId = string.IsNullOrEmpty(tenantId) ? Guid.Empty : Guid.Parse(tenantId),
                StartDate = string.IsNullOrEmpty(startDate) ? DateTime.UtcNow.AddDays(-30) : DateTime.Parse(startDate),
                EndDate = string.IsNullOrEmpty(endDate) ? DateTime.UtcNow : DateTime.Parse(endDate),
                ShowUnbilledOnly = showUnbilledOnly,
                CurrentPage = page < 1 ? 1 : page
            };

            // Get logs based on filters
            if (viewModel.ShowUnbilledOnly)
            {
                if (viewModel.TenantId != Guid.Empty)
                {
                    viewModel.TotalPages = (int)Math.Ceiling((double)viewModel.UnbilledCount / PageSize);
                }
                else if (!string.IsNullOrEmpty(viewModel.ClientId))
                {
                    viewModel.TotalPages = (int)Math.Ceiling((double)viewModel.UnbilledCount / PageSize);
                }
                else
                {
                    // Default to empty if no tenant or client specified
                    viewModel.Logs = Enumerable.Empty<SmsMessage>();
                    viewModel.TotalPages = 0;
                }
            }
            else
            {
                if (viewModel.TenantId != Guid.Empty)
                {
                    viewModel.TotalPages = 10; // In a real app, you'd get a count from the database
                }
                else if (!string.IsNullOrEmpty(viewModel.ClientId))
                {
                    viewModel.TotalPages = 10; // In a real app, you'd get a count from the database
                }
                else
                {
                    // Default to empty if no tenant or client specified
                    viewModel.Logs = Enumerable.Empty<SmsMessage>();
                    viewModel.TotalPages = 0;
                }
            }

            return View(viewModel);
        }

        [HttpPost]
        public async Task<IActionResult> MarkAsBilled(string tenantId)
        {
            if (string.IsNullOrEmpty(tenantId))
            {
                return RedirectToAction(nameof(Index));
            }

            var guid = Guid.Parse(tenantId);
            return RedirectToAction(nameof(Index), new { tenantId });
        }

        [HttpPost]
        public async Task<IActionResult> MarkSingleAsBilled(string logId)
        {
            if (string.IsNullOrEmpty(logId))
            {
                return RedirectToAction(nameof(Index));
            }

            var guid = Guid.Parse(logId);
            return RedirectToAction(nameof(Index));
        }
    }
}
