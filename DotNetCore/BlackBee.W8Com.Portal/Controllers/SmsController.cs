using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;
using BlackBee.W8Com.Portal.Models;

namespace BlackBee.W8Com.Portal.Controllers
{
    public class SmsController : Controller
    {
        //private readonly ISmsLogService _logService;
        private readonly ILogger<SmsController> _logger;

        public SmsController(ILogger<SmsController> logger)
        {
            //_logService = logService;
            _logger = logger;
        }

        public async Task<IActionResult> Details(Guid id)
        {
            //var log = await _logService.GetLogAsync(id);

            //var viewModel = new SmsDetailsViewModel
            //{
            //    Log = log
            //};

            //if (log == null)
            //{
            //    _logger.LogWarning("SMS log with ID {LogId} not found", id);
            //}
            //else
            //{
            //    _logger.LogInformation("Retrieved SMS log {LogId}", id);
            //}

            return View(null);
        }

        [HttpPost]
        public async Task<IActionResult> MarkAsBilled(Guid id)
        {
            //var log = await _logService.GetLogAsync(id);

            //if (log == null)
            //{
            //    _logger.LogWarning("SMS log with ID {LogId} not found during billing", id);
            //    return RedirectToAction("Index", "DashBoard");
            //}

            //if (!log.Billed)
            //{
            //    await _logService.MarkAsBilledAsync(new[] { id });
            //    _logger.LogInformation("Marked log {LogId} as billed", id);
            //}

            return RedirectToAction(nameof(Details), new { id });
        }
    }
}
