using System.ComponentModel.DataAnnotations;

namespace BlackBee.W8Com.Portal.ViewModels.Authentication
{
    public class RegisterViewModel
    {
        [Required]
        [EmailAddress]
        public string Email { get; set; }

        [Required]
        [StringLength(50)]
        [DataType(DataType.Password)]
        public string Password { get; set; }

        public string UserName { get; set; }

        [Required]
        public string FirstName { get; set; }

        [Required]
        public string LastName { get; set; }

        [Required]
        [Display(Name = "I agree to terms & conditions")]
        public bool AcceptTerms { get; set; }

        public string PhoneNumber { get; set; }

        /// <summary>
        /// Token Received via Invite "0000"
        /// </summary>
        [Required]
        public string Token { get; set; }
    }
}
