using System;
using BlackBee.W8Com.EF.Entities; // Updated namespace for SmsLogEntity

namespace BlackBee.W8Com.Portal.ViewModels.Dashboard
{
    public class DashboardViewModel
    {
        public int TotalMessages { get; set; }
        public int DeliveredMessages { get; set; }
        public int FailedMessages { get; set; }
        public int PendingMessages { get; set; }
        public decimal TotalCost { get; set; }
        public string Currency { get; set; } = string.Empty;
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public string TenantName { get; set; } = string.Empty;
        public Guid TenantId { get; set; }
    }
}
