using System;
using BlackBee.W8Com.Portal.Models;

namespace BlackBee.W8Com.Portal.ViewModels.ConfigurationSetting
{
    public class ConfigurationSettingAssignUnAssignViewModel
    {
        public ViewMode ViewMode { get; set; }

        public Guid? GearId { get; set; }

        public string? GearName { get; set; } = string.Empty;

        public Guid? SensorId { get; set; }

        public string? SensorName { get; set; } = string.Empty;

        public string ConfigInJson { get; set; } = string.Empty;
    }
}
