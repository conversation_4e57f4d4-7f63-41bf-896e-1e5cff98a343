using System.Collections.Generic;
using BlackBee.W8Auth.Api.Model.Models.ApplicationUser;
using BlackBee.W8Com.Portal.Models;
using Microsoft.AspNetCore.Mvc.Rendering;

namespace BlackBee.W8Com.Portal.ViewModels.ApplicationUser
{
    public class ApplicationUserViewModel : ApplicationUserModel
    {
        public ViewMode ViewMode { get; set; }
        
        public List<ApplicationUserAccessModel> Access { get; set; } = new List<ApplicationUserAccessModel>();

        public List<SelectListItem> SystemTimezones { get; set; } = new List<SelectListItem>();

    }
}
