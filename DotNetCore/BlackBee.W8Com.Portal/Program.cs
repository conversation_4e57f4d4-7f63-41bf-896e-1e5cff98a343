using BlackBee.W8Auth.Api.Wrapper;
using BlackBee.W8Base.Api.Wrapper.Handler;
using BlackBee.W8Base.Handler;
using BlackBee.W8Com.Portal;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Serilog;
using System;
using BlackBee.W8Com.Api.Wrapper;
using BlackBee.W8Com.Portal.Configurations;

try
{
    var builder = WebApplication.CreateBuilder(args);

    builder.Host.AddConfigurations();

    var logger = new LoggerConfiguration()
        .ReadFrom.Configuration(builder.Configuration)
        .Enrich.FromLogContext()
        .CreateLogger();
    builder.Logging.ClearProviders();
    builder.Logging.AddSerilog(logger);

    builder.Services.AddInfrastructure(builder.Configuration);

    builder.Services.AddApplication();

    #region Http

    builder.Services.AddTransient<W8ChallengeDelegatingHandler<BearableService>>();
    builder.Services.AddTransient<BearableService, W8AuthService>();
    builder.Services.AddHttpClient("W8Auth", httpClient =>
    {
        httpClient.BaseAddress = new Uri(builder.Configuration["W8AuthApi:Endpoint"] ?? string.Empty);
    });

    builder.Services.AddTransient<W8AuthService>();
    builder.Services.AddHttpClient("W8AuthChallenge", httpClient =>
    {
        httpClient.BaseAddress = new Uri(builder.Configuration["W8AuthApi:Endpoint"] ?? string.Empty);
    }).AddHttpMessageHandler(provider =>
    {
        var handler = provider.GetRequiredService<W8ChallengeDelegatingHandler<BearableService>>();
        handler.UniqueReferenceTarget = builder.Configuration["W8AuthApi:UniqueReference"] ?? string.Empty;
        return handler;
    });

    builder.Services.AddTransient<W8ComService>();
    builder.Services.AddHttpClient("W8ComChallenge", httpClient =>
    {
        httpClient.BaseAddress = new Uri(builder.Configuration["W8ComApi:Endpoint"] ?? string.Empty);
    }).AddHttpMessageHandler(provider =>
    {
        var handler = provider.GetRequiredService<W8ChallengeDelegatingHandler<BearableService>>();
        handler.UniqueReferenceTarget = builder.Configuration["W8ComApi:UniqueReference"] ?? string.Empty;
        return handler;
    });

    #endregion

    var app = builder.Build();

    app.UseInfrastructure(builder.Configuration);

    app.MapEndpoints();

    app.UseEndpoints(endpoints =>
    {
        //Routing Front     
        endpoints.MapControllerRoute(
            name: "default",
            pattern: "{controller=DashBoard}/{action=Index}/{id?}");

        endpoints.MapRazorPages();
    });

    app.Run();
}
catch (Exception ex) when (!ex.GetType().Name.Equals("StopTheHostException", StringComparison.Ordinal))
{
    Log.Fatal(ex, "Unhandled exception");
}
finally
{
    Log.Information("Server Shutting down...");
    Log.CloseAndFlush();
}