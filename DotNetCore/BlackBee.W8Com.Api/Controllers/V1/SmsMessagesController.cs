using System.ComponentModel.DataAnnotations;
using Asp.Versioning;
using BlackBee.W8Base.Api.Auth;
using BlackBee.W8Base.Api.Controllers;
using BlackBee.W8Base.Api.Model.Base;
using BlackBee.W8Base.Api.Model.Data;
using BlackBee.W8Base.Api.Model.Models;
using BlackBee.W8Com.Api.Helpers;
using BlackBee.W8Com.Api.Model.Enum;
using BlackBee.W8Com.Api.Model.Models.Sms;
using BlackBee.W8Com.Api.Model.Models.Sms.Providers;
using BlackBee.W8Com.Api.Providers;
using BlackBee.W8Com.Api.Providers.SmsPortal;
using BlackBee.W8Com.Api.Services;
using BlackBee.W8Com.EF;
using BlackBee.W8Com.EF.Contexts;
using BlackBee.W8Com.Roles;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using NSwag.Annotations;
using StackExchange.Redis;
using ISmsProviderFactory = BlackBee.W8Com.Api.Providers.ISmsProviderFactory;
using W8ComDbContext = BlackBee.W8Com.EF.Contexts.W8ComDbContext;

namespace BlackBee.W8Com.Api.Controllers.V1
{
    [ApiController]
    [ApiVersion("1.0")]
    public class SmsMessagesController : VersionedApiController
    {
        private readonly W8ComDbContext _context;
        private readonly ISmsQueueService _queueService;
        private readonly ISmsProviderFactory _providerFactory;
        private readonly ILogger<SmsMessagesController> _logger;

        public SmsMessagesController(
            IServiceProvider service,
            ISmsQueueService queueService,
            ISmsProviderFactory providerFactory,
            ILogger<SmsMessagesController> logger,
            W8ComDbContext context) : base(service)
        {
            _context = context;
            _queueService = queueService;
            _providerFactory = providerFactory;
            _logger = logger;
        }

        // Rest of the controller code remains unchanged
        [Route("{id:guid}")]
        [OpenApiOperation("Get Sms Message with a specific Id.", "")]
        [MapToApiVersion("1.0")]
        [AllowAnonymous]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(SmsMessageResponseModel))]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [HttpGet]
        public async Task<IActionResult> Get(Guid id, bool model)
        {
            var sms = await _context.SmsMessages.FirstOrDefaultAsync(c => c.Id == id).ConfigureAwait(false);

            GetClaims(out var tId, out var aId, out var pId, out var tz);

            if (tId == Guid.Empty || aId == Guid.Empty || pId == Guid.Empty || string.IsNullOrEmpty(tz))
            {
                return BadRequest(new BaseResponseModel()
                {
                    Errors =
                    [
                        new DataError()
                        {
                            Code = DataErrorLookup.Invalid,
                            Message = "Invalid Tenant, Account, Premis or TimeZone as extracted from the API Key!"
                        }
                    ],
                    Success = false
                });
            }
            
            if (sms == null)
            {
                return BadRequest(new BaseResponseModel()
                {
                    Errors =
                    [
                        new DataError()
                        {
                            Code = DataErrorLookup.Invalid,
                            Message = "Invalid Id"
                        }
                    ],
                    Success = false
                });
            }

            try
            {
                var modelHelper = new ModelHelper(_context);

                sms.TenantId = tId;
                sms.AccountId = aId;
                sms.PremisId = pId;
                
                return Ok(new SmsMessageResponseModel()
                {
                    SmsMessage = modelHelper.ToModel(sms, model)
                });
            }
            catch (Exception ex)
            {
                var err = new DataError
                {
                    Code = DataErrorLookup.Exception,
                    Message = $"{ex.Message}"
                };

                return UnprocessableEntity(new BaseResponseModel()
                {
                    Errors = [err],
                    Success = false
                });
            }
        }

        [OpenApiOperation("Send Sms Message.", "")]
        [MapToApiVersion("1.0")]
        [Authorize(Policy = CustomBearerDefaults.ApiKeyAuthenticationScheme, Roles = ApiSmsMessageRoles.Create)]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(SmsMessageResponseModel))]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [HttpPost]
        public async Task<IActionResult> Post([FromBody] SmsMessagePostModel model)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(new BaseResponseModel()
                {
                    Errors =
                    [
                        new DataError()
                        {
                            Code = DataErrorLookup.Invalid,
                            Message = "Invalid payload"
                        }
                    ],
                    Success = false
                });
            }

            ISmsProvider? provider = null;
            
            if (!string.IsNullOrEmpty(model.PreferredProvider))
            {
                provider = _providerFactory.GetProvider(model.PreferredProvider);
                if (provider == null)
                {
                    return BadRequest(new BaseResponseModel()
                    {
                        Errors =
                        [
                            new DataError()
                            {
                                Code = DataErrorLookup.Invalid,
                                Message = $"Provider '{model.PreferredProvider}' not found"
                            }
                        ],
                        Success = false
                    });
                }
            }

            GetClaims(out var tId, out var aId, out var pId, out var tz);

            if (tId == Guid.Empty || aId == Guid.Empty || pId == Guid.Empty || string.IsNullOrEmpty(tz))
            {
                return BadRequest(new BaseResponseModel()
                {
                    Errors =
                    [
                        new DataError()
                         {
                             Code = DataErrorLookup.Invalid,
                             Message = "Invalid Tenant, Account, Premis or TimeZone as extracted from the API Key!"
                         }
                    ],
                    Success = false
                });
            }

            try
            {
               
                var modelHelper = new ModelHelper(_context);
                SmsMessage smsMessage = modelHelper.FromModel(model);

                smsMessage.Provider = provider?.Name ?? "";
                smsMessage.TenantId = tId;
                smsMessage.AccountId = aId;
                smsMessage.PremisId = pId;
                smsMessage.Billed = false;
                smsMessage.IsActive = true;
                smsMessage.Status = SmsMessageStatus.Received;
                smsMessage.ReceivedAt = DateTime.UtcNow;

                var validationContext = new ValidationContext(smsMessage);
                Validator.ValidateObject(smsMessage, validationContext);
                
                _context.SmsMessages.Add(smsMessage);

                await _context.SaveChangesAsyncExtended().ConfigureAwait(false);
                
                var message = await _queueService.EnqueueAsync(smsMessage);

                _logger.LogInformation("SMS request {RequestId} enqueued as message {MessageId}", smsMessage.Id, message.Id);
                
                return Ok(new SmsMessageResponseModel()
                {
                    SmsMessage = modelHelper.ToModel(smsMessage, true)
                });
            }
            catch (ValidationException e)
            {
                var err = new DataError
                {
                    Code = DataErrorLookup.Exception,
                    Message = $"{WriteDbValidationException(e)}"
                };

                return UnprocessableEntity(new BaseResponseModel()
                {
                    Errors = [err],
                    Success = false
                });
            }
            catch (DbUpdateException ex)
            {
                var err = new DataError
                {
                    Code = DataErrorLookup.Exception,
                    Message = $"{WriteDbUpdateException(ex)}"
                };

                return UnprocessableEntity(new BaseResponseModel()
                {
                    Errors = [err],
                    Success = false
                });
            }
            catch (Exception ex)
            {
                var err = new DataError
                {
                    Code = DataErrorLookup.Exception,
                    Message = $"{WriteException(ex)}"
                };

                return UnprocessableEntity(new BaseResponseModel()
                {
                    Errors = [err],
                    Success = false
                });
            }
        }

        [OpenApiOperation("Get List of Message Providers.", "")]
        [MapToApiVersion("1.0")]
        [Authorize(Policy = CustomBearerDefaults.ApiKeyAuthenticationScheme, Roles = ApiSmsMessageRoles.Create)]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(SmsMessageProvidersResponseModel))]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [HttpGet]
        public IActionResult GetProviders()
        {
            try
            {
                var providers = _providerFactory.GetAllProviders().Select(p => p.Name);

                var pg = providers.Select(provider => new SmsMessageProviderModel
                {
                    Name = provider
                }).ToList();
                
                return Ok(new SmsMessageProvidersResponseModel()
                {
                    SmsMessageProviders = new PaginationListModel<SmsMessageProviderModel>()
                    {
                        HasNextPage = false,
                        HasPreviousPage = false,
                        PageIndex = 0,
                        PageResult = pg,
                        PageSize = 100,
                        ThisCount = pg.Count,
                        TotalCount = pg.Count,
                        TotalPages = pg.Count/100
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting providers");
                return StatusCode(500, "An error occurred while processing your request");
            }
        }
        
        private void GetClaims(out Guid tId, out Guid aId, out Guid pId, out string? tz)
        {
            aId = Guid.Empty;
            tId = Guid.Empty;
            aId = Guid.Empty;
            pId = Guid.Empty;
            tz = string.Empty;

            if (HttpContext.User.Claims.Any())
            {
                var tenantId = HttpContext.User.Claims.FirstOrDefault(c => c.Type == "TenantId")?.Value;
                var accountId = HttpContext.User.Claims.FirstOrDefault(c => c.Type == "AccountId")?.Value;
                var premisId = HttpContext.User.Claims.FirstOrDefault(c => c.Type == "PremisId")?.Value;
                tz = HttpContext.User.Claims.FirstOrDefault(c => c.Type == "TimeZone")?.Value;

                if (!string.IsNullOrEmpty(tenantId))
                {
                    tId = Guid.Parse(tenantId);
                }

                if (!string.IsNullOrEmpty(accountId))
                {
                    aId = Guid.Parse(accountId);
                }

                if (!string.IsNullOrEmpty(premisId))
                {
                    pId = Guid.Parse(premisId);
                }
            }
        }
    }
}