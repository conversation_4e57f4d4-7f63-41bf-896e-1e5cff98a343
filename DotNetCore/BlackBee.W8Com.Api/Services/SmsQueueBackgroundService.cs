
using BlackBee.W8Com.Api.Model.Enum;
using BlackBee.W8Com.Api.Providers;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace BlackBee.W8Com.Api.Services
{
    public class SmsQueueBackgroundService : BackgroundService
    {
        private readonly ILogger<SmsQueueBackgroundService> _logger;
        private readonly IServiceProvider _serviceProvider;
        private readonly TimeSpan _delay = TimeSpan.FromSeconds(30); // Process every 30 seconds
        private readonly int _batchSize = 50; // Process 50 messages per batch

        public SmsQueueBackgroundService(
            ILogger<SmsQueueBackgroundService> logger,
            IServiceProvider serviceProvider)
        {
            _logger = logger;
            _serviceProvider = serviceProvider;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("SMS Queue Background Service is starting");

            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    await ProcessQueuedMessages(stoppingToken);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error occurred while processing queued messages");
                }

                await Task.Delay(_delay, stoppingToken);
            }

            _logger.LogInformation("SMS Queue Background Service is stopping");
        }

        private async Task ProcessQueuedMessages(CancellationToken stoppingToken)
        {
            using var scope = _serviceProvider.CreateScope();
            var queueService = scope.ServiceProvider.GetRequiredService<ISmsQueueService>();
            var providerFactory = scope.ServiceProvider.GetRequiredService<ISmsProviderFactory>();

            // Get queued messages
            var messages = await queueService.GetMessagesByStatusAsync(SmsMessageStatus.Queued, _batchSize);
            
            foreach (var message in messages)
            {
                if (stoppingToken.IsCancellationRequested)
                    break;

                try
                {
                    // Update status to Processing
                    await queueService.UpdateMessageStatusAsync(message.Id, SmsMessageStatus.Processing);

                    // Get appropriate provider and send message
                    var provider = providerFactory.GetBestProviderForMessage(message) as ISmsProvider;
                    if (provider == null)
                    {
                        throw new InvalidOperationException($"Could not get valid SMS provider for message {message.Id}");
                    }

                    var success = await provider.SendMessageAsync(message);

                    // Update final status
                    var finalStatus = success ? SmsMessageStatus.Sent : SmsMessageStatus.Failed;
                    await queueService.UpdateMessageStatusAsync(message.Id, finalStatus);

                    _logger.LogInformation(
                        "Message {MessageId} processed with status {Status} using provider {Provider}", 
                        message.Id, finalStatus, provider.Name);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, 
                        "Failed to process message {MessageId}. Setting status to Failed", 
                        message.Id);
                    
                    try
                    {
                        await queueService.UpdateStatusAsync(message.Id, SmsMessageStatus.Failed, ex.Message);
                    }
                    catch (Exception updateEx)
                    {
                        _logger.LogError(updateEx, 
                            "Failed to update status for message {MessageId}", 
                            message.Id);
                    }
                }
            }
        }
    }
}
