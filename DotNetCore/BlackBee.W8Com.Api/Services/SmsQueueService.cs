
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using BlackBee.W8Com.Api.Model.Enum;
using BlackBee.W8Com.Api.Model.Models.Sms.Queue;
using BlackBee.W8Com.EF;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace BlackBee.W8Com.Api.Services
{
    public class SmsQueueService : ISmsQueueService
    {
        private readonly ILogger<SmsQueueService> _logger;
        private readonly W8ComDbContext _dbContext;
        private readonly ISmsProviderFactory _smsProviderFactory;

        public SmsQueueService(
            ILogger<SmsQueueService> logger,
            W8ComDbContext dbContext,
            ISmsProviderFactory smsProviderFactory)
        {
            _logger = logger;
            _dbContext = dbContext;
            _smsProviderFactory = smsProviderFactory;
        }

        public async Task<SmsMessage> EnqueueAsync(SmsMessage message)
        {
            try
            {
                message.Status = SmsMessageStatus.Queued;
                message.QueuedAt = DateTime.UtcNow;
                message.LastAttemptAt = DateTime.UtcNow;
                message.AttemptCount = 0;
                
                await _dbContext.SmsMessages.AddAsync(message);
                await _dbContext.SaveChangesAsync();
                
                _logger.LogInformation("Message {MessageId} enqueued successfully", message.Id);
                return message;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to enqueue message {MessageId}", message.Id);
                throw;
            }
        }

        public async Task<SmsMessageQueueModel?> DequeueAsync()
        {
            try
            {
                var message = await _dbContext.SmsMessages
                    .Where(m => m.Status == SmsMessageStatus.Queued)
                    .OrderBy(m => m.QueuedAt)
                    .FirstOrDefaultAsync();

                if (message == null)
                {
                    return null;
                }

                // Convert to queue model
                var queueModel = new SmsMessageQueueModel
                {
                    Id = message.Id,
                    To = message.To,
                    From = message.From,
                    Message = message.Message,
                    Provider = message.Provider,
                    AttemptCount = message.AttemptCount,
                    LastAttemptAt = message.LastAttemptAt,
                    IsActive = true  // Set required IsActive property
                };

                // Update message status to Processing
                await UpdateStatusAsync(message.Id, SmsMessageStatus.Processing);

                return queueModel;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to dequeue next message");
                throw;
            }
        }

        public async Task<SmsMessage?> GetMessageAsync(Guid id)
        {
            try
            {
                return await _dbContext.SmsMessages.FindAsync(id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get message {MessageId}", id);
                throw;
            }
        }

        public async Task<IEnumerable<SmsMessage>> GetMessagesByStatusAsync(SmsMessageStatus status, int count)
        {
            try
            {
                return await _dbContext.SmsMessages
                    .Where(m => m.Status == status)
                    .OrderBy(m => m.QueuedAt)
                    .Take(count)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get messages with status {Status}", status);
                throw;
            }
        }

        public async Task<int> GetQueueLengthAsync()
        {
            try
            {
                return await _dbContext.SmsMessages
                    .CountAsync(m => m.Status == SmsMessageStatus.Queued);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get queue length");
                throw;
            }
        }

        public async Task UpdateStatusAsync(Guid messageId, SmsMessageStatus status, string? errorMessage = null)
        {
            try
            {
                var message = await _dbContext.SmsMessages.FindAsync(messageId);
                if (message == null)
                {
                    throw new KeyNotFoundException($"Message with ID {messageId} not found");
                }

                message.Status = status;
                message.LastAttemptAt = DateTime.UtcNow;
                message.AttemptCount++;
                message.ErrorMessage = errorMessage;

                switch (status)
                {
                    case SmsMessageStatus.Sent:
                        message.SentAt = DateTime.UtcNow;
                        break;
                    case SmsMessageStatus.Processing:
                        message.ProcessedAt = DateTime.UtcNow;
                        break;
                    case SmsMessageStatus.Billed:
                        message.BilledAt = DateTime.UtcNow;
                        message.Billed = true;
                        break;
                }

                await _dbContext.SaveChangesAsync();
                _logger.LogInformation("Message {MessageId} status updated to {Status}", messageId, status);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to update status for message {MessageId}", messageId);
                throw;
            }
        }

        public async Task UpdateMessageStatusAsync(Guid messageId, SmsMessageStatus status)
        {
            await UpdateStatusAsync(messageId, status);
        }
    }
}
