
using Asp.Versioning;
using BlackBee.W8Base.Api.Auth.ApiKey;
using BlackBee.W8Base.Api.Auth.Jwt;
using BlackBee.W8Base.Api.Cors;
using BlackBee.W8Base.Api.Filters;
using BlackBee.W8Base.Api.Health;
using BlackBee.W8Base.Api.Middleware;
using BlackBee.W8Base.Api.NSwag;
using BlackBee.W8Base.Api.SecurityHeaders;
using BlackBee.W8Base.Caching;
using BlackBee.W8Base.Common;
using BlackBee.W8Base.Db;
using BlackBee.W8Base.FileStorage;
using BlackBee.W8Base.Mailing;
using BlackBee.W8Com.EF; // Updated namespace
using FluentValidation;
using Microsoft.AspNetCore.Diagnostics.HealthChecks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Formatters;
using Microsoft.AspNetCore.Mvc.Infrastructure;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using System.Net.Mime;
using System.Reflection;
using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;
using BlackBee.W8Base.Api.BackgroundJobs;

namespace BlackBee.W8Com.Api;

public static class Startup
{
    public static IServiceCollection AddApplication(this IServiceCollection services)
    {
        var assembly = Assembly.GetExecutingAssembly();

        return services
            .AddValidatorsFromAssembly(assembly);
    }

    public static IServiceCollection AddInfrastructure(this IServiceCollection services, IConfiguration config)
    {
        services
            .AddMvc(config)
            .AddApiVersioning()
            .AddLogging()
            .AddApiKeyAuth(config)
            .AddBackgroundJobs(config)
            .AddCaching(config)
            .AddCorsPolicy(config)
            .AddExceptionMiddleware()
            .AddHealthCheck(config)
            .AddNSwag(config)
            .AddFileStorage(config)
            .AddDatabaseSettings(config)
            .AddDatabase(config)
            .AddMailing(config)
            .AddRouting(options => options.LowercaseUrls = true)
            .AddServices();

        //services.AddResponseCaching();

        services.AddPortableObjectLocalization();

        services
	        .Configure<RequestLocalizationOptions>(options => options
		        .AddSupportedCultures("fr", "cs")
		        .AddSupportedUICultures("fr", "cs"));

        return services;
	}

    public static IApplicationBuilder UseInfrastructure(this IApplicationBuilder builder, IConfiguration config) =>
        builder
            .UseRequestLocalization()
            .UseStaticFiles()
            .UseSecurityHeaders(config)
            .UseFileStorage()
            .UseExceptionMiddleware()
            .UseRouting()
            .UseCorsPolicy()
            .UseAuthentication()
            .UseAuthorization()
            .UseNSwag(config)
            .UseHangfireDashboard(config);


    #region Custom

    private static IServiceCollection AddApiVersioning(this IServiceCollection services) =>
		services.AddApiVersioning(config =>
		{
			config.DefaultApiVersion = new ApiVersion(1, 0);
			config.AssumeDefaultVersionWhenUnspecified = true;
			config.ReportApiVersions = true;
		}).Services;

	private static IServiceCollection AddMvc(this IServiceCollection services, IConfiguration config)
	{
		services.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();
		services.AddSingleton<IActionContextAccessor, ActionContextAccessor>();

		services.AddControllers(options =>
		{
			options.Filters.Add(new HttpResponseExceptionFilter());
			options.AllowEmptyInputInBodyModelBinding = true;
		})
			.ConfigureApiBehaviorOptions(options =>
			{
				options.InvalidModelStateResponseFactory = context =>
				{
					var result = new BadRequestObjectResult(context.ModelState);

					result.ContentTypes.Add(MediaTypeNames.Application.Json);
					result.ContentTypes.Add(MediaTypeNames.Application.Xml);

					return result;
				};
			});

		services.AddMvcCore(options =>
		{
			options.AllowEmptyInputInBodyModelBinding = true;
			foreach (var formatter in options.InputFormatters)
			{
				if (formatter.GetType() == typeof(SystemTextJsonInputFormatter))
					((SystemTextJsonInputFormatter)formatter).SupportedMediaTypes.Add(
						Microsoft.Net.Http.Headers.MediaTypeHeaderValue.Parse("text/plain"));
			}
		}).AddJsonOptions(options =>
		{
			options.JsonSerializerOptions.Converters.Add(new JsonStringEnumConverter());
			options.JsonSerializerOptions.PropertyNameCaseInsensitive = true;
		});

		return services;
	}

	public static IServiceCollection AddDatabase(this IServiceCollection services, IConfiguration config)
	{
		var databaseSettings = config.GetSection(nameof(DatabaseSettings)).Get<DatabaseSettings>();

		switch (databaseSettings?.DbProvider)
		{
			case DbProviderKeys.Npgsql:
                // Register DbContext with correct namespace
                services.AddDbContext<W8ComDbContext>(options => options.UseNpgsql(databaseSettings.ConnectionString));

				break;

			default:
				throw new InvalidOperationException($"DB Provider {databaseSettings?.DbProvider} is not supported.");
		}

		return services;
	}

	#endregion


	public static IEndpointRouteBuilder MapEndpoints(this IEndpointRouteBuilder builder)
	{
		builder.MapControllers().RequireAuthorization();
		builder.MapHealthCheck();
		return builder;
	}

	#region HealthCheck

	private static IEndpointConventionBuilder MapHealthCheck(this IEndpointRouteBuilder endpoints)
	{
		var item = endpoints.MapHealthChecks("/api/health", new HealthCheckOptions
		{
			AllowCachingResponses = false,
			ResultStatusCodes =
				{
					[HealthStatus.Healthy] = StatusCodes.Status200OK,
					[HealthStatus.Degraded] = StatusCodes.Status200OK,
					[HealthStatus.Unhealthy] = StatusCodes.Status503ServiceUnavailable
				},
			ResponseWriter = WriteResponse
		}).AllowAnonymous();

		return item;
	}

	private static Task WriteResponse(HttpContext context, HealthReport healthReport)
	{
		context.Response.ContentType = "application/json; charset=utf-8";

		var options = new JsonWriterOptions { Indented = true };

		using var memoryStream = new MemoryStream();
		using (var jsonWriter = new Utf8JsonWriter(memoryStream, options))
		{
			jsonWriter.WriteStartObject();
			jsonWriter.WriteString("status", healthReport.Status.ToString());
			jsonWriter.WriteStartObject("results");

			foreach (var healthReportEntry in healthReport.Entries)
			{
				jsonWriter.WriteStartObject(healthReportEntry.Key);
				jsonWriter.WriteString("status",
					healthReportEntry.Value.Status.ToString());
				jsonWriter.WriteString("description",
					healthReportEntry.Value.Description);
				jsonWriter.WriteStartObject("data");

				foreach (var item in healthReportEntry.Value.Data)
				{
					jsonWriter.WritePropertyName(item.Key);

					JsonSerializer.Serialize(jsonWriter, item.Value,
						item.Value?.GetType() ?? typeof(object));
				}

				jsonWriter.WriteEndObject();
				jsonWriter.WriteEndObject();
			}

			jsonWriter.WriteEndObject();
			jsonWriter.WriteEndObject();
		}

		return context.Response.WriteAsync(Encoding.UTF8.GetString(memoryStream.ToArray()));
	}

	private static IServiceCollection AddHealthCheck(this IServiceCollection services, IConfiguration config)
	{
		//Include:
		//AspNetCore.HealthChecks.Redis
		//Microsoft.Extensions.Diagnostics.HealthChecks.EntityFrameworkCore

		var db = config.GetSection("DatabaseSettings").Get<DatabaseSettings>();
		var cache = config.GetSection("CacheSettings").Get<CacheSettings>();

        if (db is { ConnectionString: not null } && cache is { UseRedisMultiplexerCache: true })
        {
			var items = services.AddHealthChecks()
                .AddRedis(redisConnectionString: $"{cache.RedisHost}:{cache.RedisPort}")
                .AddDbContextCheck<W8ComDbContext>()
				.AddCheck<HealthCheck>("Api").Services;

			return items;
		}
        else if (db is { ConnectionString: not null } && cache is { UseRedisCache: true })
        {
            var items = services.AddHealthChecks()
                .AddRedis(redisConnectionString: $"{cache.RedisHost}:{cache.RedisPort}")
                .AddDbContextCheck<W8ComDbContext>()
                .AddCheck<HealthCheck>("Api").Services;

            return items;
        }
		else
		{
			var items = services.AddHealthChecks()
				.AddCheck<ApiHealthCheck>("Api").Services;

			return items;
		}
	}

	public class ApiHealthCheck : IHealthCheck
	{
		public Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
		{
			var isHealthy = true;

            if (isHealthy)
			{
				return Task.FromResult(
					HealthCheckResult.Healthy("A healthy result."));
			}

			return Task.FromResult(
				new HealthCheckResult(
					context.Registration.FailureStatus, "An unhealthy result."));
		}
	}

	#endregion
}
