using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace BlackBee.W8Com.EF.Base
{
    public class ConfigurationBaseEntity : W8ComBaseEntity
    {
        [Required]
        [Column("settings_group")]
        [StringLength(500)]
        public string Group { get; set; }

        [Required]
        [Column("settings_key")]
        [StringLength(500)]
        public string Key { get; set; }

        [Required]
        [Column("settings_value")]
        [StringLength(10000)]
        public string Value { get; set; }
    }
}
