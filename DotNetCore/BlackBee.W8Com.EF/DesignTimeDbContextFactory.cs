
using System;
using System.IO;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;
using Microsoft.Extensions.Configuration;

namespace BlackBee.W8Com.EF.Contexts;

public class DesignTimeDbContextFactory : IDesignTimeDbContextFactory<W8ComDbContext>
{
    public W8ComDbContext CreateDbContext(string[] args)
    {
        // Get the directory where the EF project is located
        string projectDir = Directory.GetCurrentDirectory();
        
        var configuration = new ConfigurationBuilder()
            .SetBasePath(projectDir)
            .AddJsonFile(Path.Combine("Configurations", "database.json"), optional: false)
            .AddJsonFile(Path.Combine("Configurations", "database.Development.json"), optional: true)
            .AddEnvironmentVariables()
            .Build();

        var connectionString = configuration.GetSection("DatabaseSettings:ConnectionString").Value;
        
        if (string.IsNullOrEmpty(connectionString))
        {
            throw new InvalidOperationException(
                "Could not find a valid connection string in database.json. Please ensure DatabaseSettings:ConnectionString is properly configured.");
        }

        var builder = new DbContextOptionsBuilder<W8ComDbContext>();
        builder.UseNpgsql(connectionString, options =>
        {
            options.EnableRetryOnFailure(3);
            options.CommandTimeout(30);
        });
        
        return new W8ComDbContext(builder.Options);
    }
}
